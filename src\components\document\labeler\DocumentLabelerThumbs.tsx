import Tooltip from '@components/shared/tooltip/Tooltip.tsx';
import { sleep } from '@shared/helpers/helpers';
import { useGetDoctypesQuery } from '@shared/helpers/rtk-query/firestoreApi.ts';
import { useDocumentOperations } from '@shared/hooks/useDocumentOperations';
import { useImageLoader } from '@shared/hooks/useImageLoader';
import { useNavigationAnimations } from '@shared/hooks/useNavigationAnimations';
import { UrlParams } from '@shared/models/generic';
import { selectActiveDocument, selectActivePageNo } from '@shared/store/documentSlice';
import labelerSlice from '@shared/store/labelerSlice.ts';
import { useDispatch, useSelector } from '@shared/store/store';
import { ReactComponent as DocumentIcon } from '@svg/document-icon.svg';
import clsx from 'clsx';
import React, { useEffect, useRef, useState } from 'react';
import { useLocation, useParams } from 'react-router';
import useAsyncEffect from 'use-async-effect';
import s from './viewer/labeler-view.module.scss';

interface Props {}

const DocumentLabelerThumbs: React.FC<Props> = () => {
  const { docId, inboxId }: UrlParams = useParams();
  // Get state from Redux
  const activeDocument = useSelector(selectActiveDocument);
  const activePageNo = useSelector(selectActivePageNo);
  // Get operations from the hook
  const { setActivePage } = useDocumentOperations();
  const pageImagesMap = useSelector((state) => state.document.pageImagesMap);
  const navigationAnimationState = useSelector((state) => state.document.navigationAnimationState);

  const { all: docTypes } = useGetDoctypesQuery({ inboxId }).data ?? {};
  const isThumbsVisible = useSelector((state) => state.labeler.isThumbsVisible);

  const dispatch = useDispatch();
  const location = useLocation();
  const historical = location.pathname.includes('historical');

  useEffect(() => {
    if (activeRef.current) {
      const el = activeRef.current as HTMLImageElement;
      el.scrollIntoView({ inline: 'center', behavior: 'smooth', block: 'center' });
    }
  }, [activePageNo]);

  const currentThumbs = pageImagesMap?.[docId];

  const [delayedActive, setDelayedActive] = useState(false);

  useAsyncEffect(async () => {
    if (isThumbsVisible) {
      sleep(300).then(() => setDelayedActive(true));
    } else {
      setDelayedActive(false);
    }
  }, [isThumbsVisible]);

  const activeRef = useRef(null);

  // Enhanced image loading hooks
  const imageLoader = useImageLoader({
    enablePreloading: true,
    preloadRange: 1,
    transitionDuration: 200,
    enableThumbnailFallback: false, // Thumbnails don't need fallback
  });

  const navigationAnimations = useNavigationAnimations({
    pageTransitionDuration: 200,
    documentTransitionDuration: 300,
  });

  // Load thumbnails for all pages when document changes
  useEffect(() => {
    if (activeDocument && inboxId && docId) {
      const totalPages = activeDocument.dimensions?.length || 0;
      if (totalPages > 0) {
        // Load thumbnails for all pages
        for (let pageNo = 1; pageNo <= totalPages; pageNo++) {
          imageLoader.loadImage(inboxId, docId, pageNo, 'thumb', 'low');
        }
      }
    }
  }, [activeDocument, inboxId, docId, imageLoader]);

  const titleFontSize = (title: string) => {
    const textLength = title.length;
    const maxSize = 14;
    const minSize = 12;
    const shrinkStartLength = 12;
    const shrinkEndLength = 25;

    if (textLength <= shrinkStartLength) {
      return maxSize;
    }
    if (textLength > shrinkStartLength && textLength <= shrinkEndLength) {
      const sizeDecrementPerChar = (maxSize - minSize) / (shrinkEndLength - shrinkStartLength);
      return maxSize - (textLength - shrinkStartLength) * sizeDecrementPerChar;
    }
    return minSize;
  };

  return (
    <div data-tour={'thumbs'} className={clsx(s.page_thumbs)}>
      {activeDocument?.topology?.parts && currentThumbs ? (
        <>
          {activeDocument.topology.parts.map((group) => {
            const validTypes = docTypes?.filter(
              (e) =>
                e.topologyType === (group.topologyType !== undefined ? group.topologyType : 'document') &&
                !e.isArchived,
            );

            if (group.archived) return null;

            const active = group.pages
              .filter((e) => !e.archived)
              .find((p) => p.bundlePageNo === activePageNo);
            const doctype = group.docTypeDetails;
            const subType = group.docTypeDetails?.subTypeDetails;

            let isLowConfidence = false;
            let name = group.name;
            if (doctype) {
              name = doctype?.name;
              if (subType) name += ` - ${subType.name}`;
              isLowConfidence = group.confidence < doctype.approvalThreshold / 100 || !group.confidence;
            }
            return (
              <div className={s.group} key={group.id}>
                <div
                  className={clsx(
                    s.group_header,
                    { [s.visible]: isThumbsVisible },
                    { [s.low_confidence]: isLowConfidence },
                    {
                      [s.failed]:
                        group.docTypeId === '@PB_NOTYPE' ||
                        (group.docTypeId == null && validTypes?.length > 1),
                    },
                    { [s.active]: active && isThumbsVisible && !historical },
                  )}
                >
                  <div className={s.group_wrapper}>
                    <div
                      className={s.name}
                      onClick={() => {
                        const activePages = group.pages.filter((e) => !e.archived);
                        setActivePage(activePages[0].bundlePageNo);
                      }}
                    >
                      {!doctype && <DocumentIcon style={{ height: 12, marginBottom: 1, marginLeft: -16 }} />}
                      {name.length > 15 ? (
                        <Tooltip
                          tooltipStyle={{ maxWidth: 180, padding: 10, lineHeight: 1.2 }}
                          position={'top'}
                          content={name}
                        >
                          <span style={{ fontSize: titleFontSize(name) }}>{name}</span>
                        </Tooltip>
                      ) : (
                        <span style={{ fontSize: titleFontSize(name) }}>{name}</span>
                      )}
                    </div>
                  </div>
                </div>
                <div className={s.group_items}>
                  {group.pages.map((page) => {
                    const img = currentThumbs[page.bundlePageNo]?.thumbUrl;
                    const imageData = imageLoader.getCurrentImage(docId, page.bundlePageNo);
                    const isPageAnimating = navigationAnimations.getAnimationState(
                      docId,
                      page.bundlePageNo,
                      'page',
                    );

                    if (page.archived) return null;

                    const handleThumbnailClick = () => {
                      dispatch(labelerSlice.actions.setActiveEntityPair(null));

                      // Trigger navigation animation
                      imageLoader.triggerNavigationAnimation(docId, page.bundlePageNo, 'page');

                      setActivePage(page.bundlePageNo);
                    };

                    const handleThumbnailError = () => {
                      console.error(`Error loading thumbnail for page ${page.bundlePageNo}`);
                      // Retry loading the thumbnail
                      imageLoader.loadImage(inboxId, docId, page.bundlePageNo, 'thumb', 'normal');
                    };

                    return (
                      <div
                        key={page.pageNo + activeDocument.id}
                        className={clsx(s.thumbnailContainer, {
                          [s.active]: activePageNo === page.bundlePageNo,
                          [s.visible]: delayedActive,
                          [s.loading]: imageData.isLoading,
                          [s.animating]: isPageAnimating,
                          [s.hasError]: imageData.hasError,
                        })}
                        ref={activePageNo === page.bundlePageNo ? activeRef : null}
                        onClick={handleThumbnailClick}
                      >
                        {img ? (
                          <img
                            data-hj-suppress
                            src={img}
                            alt={`Page ${page.bundlePageNo}`}
                            onError={handleThumbnailError}
                            className={s.thumbnailImage}
                            style={navigationAnimations.getAnimationStyles('thumbnail')}
                          />
                        ) : (
                          <div className={s.thumbnailPlaceholder}>
                            <div className={s.thumbnailSpinner} />
                          </div>
                        )}

                        {imageData.isLoading && (
                          <div className={s.thumbnailLoadingOverlay}>
                            <div className={s.thumbnailSpinner} />
                          </div>
                        )}

                        {imageData.hasError && (
                          <div className={s.thumbnailErrorOverlay}>
                            <span>⚠️</span>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </>
      ) : (
        <div className={s.group_items}>
          {activeDocument?.dimensions.map((_, i) => {
            const pageNo = i + 1;
            if (!currentThumbs) return;
            const img = currentThumbs[pageNo];
            const imageData = imageLoader.getCurrentImage(docId, pageNo);
            const isPageAnimating = navigationAnimations.getAnimationState(docId, pageNo, 'page');

            if (!img) return null;

            const handleThumbnailClick = () => {
              dispatch(labelerSlice.actions.setActiveEntityPair(null));

              // Trigger navigation animation
              imageLoader.triggerNavigationAnimation(docId, pageNo, 'page');

              setActivePage(pageNo);
            };

            const handleThumbnailError = () => {
              console.error(`Error loading thumbnail for page ${pageNo}`);
              // Retry loading the thumbnail
              imageLoader.loadImage(inboxId, docId, pageNo, 'thumb', 'normal');
            };

            return (
              <div
                key={pageNo + docId}
                className={clsx(s.thumbnailContainer, {
                  [s.active]: activePageNo === pageNo,
                  [s.visible]: delayedActive,
                  [s.loading]: imageData.isLoading,
                  [s.animating]: isPageAnimating,
                  [s.hasError]: imageData.hasError,
                })}
                ref={activePageNo === pageNo ? activeRef : null}
                onClick={handleThumbnailClick}
              >
                {img.thumbUrl ? (
                  <img
                    data-hj-suppress
                    src={img.thumbUrl}
                    alt={`Page ${pageNo}`}
                    onError={handleThumbnailError}
                    className={s.thumbnailImage}
                    style={navigationAnimations.getAnimationStyles('thumbnail')}
                  />
                ) : (
                  <div className={s.thumbnailPlaceholder}>
                    <div className={s.thumbnailSpinner} />
                  </div>
                )}

                {imageData.isLoading && (
                  <div className={s.thumbnailLoadingOverlay}>
                    <div className={s.thumbnailSpinner} />
                  </div>
                )}

                {imageData.hasError && (
                  <div className={s.thumbnailErrorOverlay}>
                    <span>⚠️</span>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default DocumentLabelerThumbs;
