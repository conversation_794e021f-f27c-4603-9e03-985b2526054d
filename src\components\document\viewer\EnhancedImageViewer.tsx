import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import clsx from 'clsx';
import { RootState } from '@shared/store/store';
import { useImageLoader } from '@shared/hooks/useImageLoader';
import { useNavigationAnimations } from '@shared/hooks/useNavigationAnimations';
import { useLoadingStates } from '@shared/hooks/useLoadingStates';
import { NavigationTransition, LoadingOverlay, ImageTransition } from '@components/shared/animations/NavigationTransition';
import { setNavigationAnimationState } from '@shared/store/documentSlice';
import styles from './EnhancedImageViewer.module.scss';

export interface EnhancedImageViewerProps {
  docId: string;
  inboxId: string;
  pageNo: number;
  className?: string;
  style?: React.CSSProperties;
  onImageLoad?: () => void;
  onImageError?: (error: Error) => void;
  enablePreloading?: boolean;
  showLoadingIndicator?: boolean;
  animationDuration?: number;
}

export const EnhancedImageViewer: React.FC<EnhancedImageViewerProps> = ({
  docId,
  inboxId,
  pageNo,
  className = '',
  style = {},
  onImageLoad,
  onImageError,
  enablePreloading = true,
  showLoadingIndicator = true,
  animationDuration = 300,
}) => {
  const dispatch = useDispatch();
  
  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const previousPageRef = useRef<number>(pageNo);
  const previousDocRef = useRef<string>(docId);
  
  // Local state
  const [isImageVisible, setIsImageVisible] = useState(false);
  const [hasImageLoaded, setHasImageLoaded] = useState(false);
  
  // Redux selectors
  const activeDocument = useSelector((state: RootState) => state.document.activeDocument);
  const navigationAnimationState = useSelector((state: RootState) => state.document.navigationAnimationState);
  const pageImagesMap = useSelector((state: RootState) => state.document.pageImagesMap);
  
  // Custom hooks
  const imageLoader = useImageLoader({
    enablePreloading,
    preloadRange: 2,
    transitionDuration: animationDuration,
    enableThumbnailFallback: true,
  });
  
  const navigationAnimations = useNavigationAnimations({
    pageTransitionDuration: animationDuration,
    documentTransitionDuration: animationDuration + 100,
  });
  
  const loadingStates = useLoadingStates({
    minLoadingDuration: 200,
    showProgressAfter: 500,
  });
  
  // Get current image data
  const currentImageData = imageLoader.getCurrentImage(docId, pageNo);
  const { imageUrl, thumbUrl, isShowingThumbnail, isLoading, hasError } = currentImageData;
  
  // Determine which image to display
  const displayImageUrl = imageUrl || thumbUrl;
  const shouldShowImage = displayImageUrl && !hasError;
  
  // Detect navigation changes
  const isPageChange = previousPageRef.current !== pageNo && previousDocRef.current === docId;
  const isDocumentChange = previousDocRef.current !== docId;
  
  // Handle image loading
  const handleImageLoad = useCallback(() => {
    setHasImageLoaded(true);
    setIsImageVisible(true);
    onImageLoad?.();
    
    // Stop loading states
    loadingStates.stopLoading('page', true);
    if (isShowingThumbnail) {
      loadingStates.stopLoading('thumbnail', true);
    }
  }, [onImageLoad, loadingStates, isShowingThumbnail]);
  
  const handleImageError = useCallback((error: Error) => {
    console.error(`Error loading image for page ${pageNo}:`, error);
    setHasImageLoaded(false);
    setIsImageVisible(false);
    onImageError?.(error);
    
    // Stop loading states with error
    loadingStates.stopLoading('page', false, 'Failed to load image');
  }, [pageNo, onImageError, loadingStates]);
  
  // Load images when component mounts or props change
  useEffect(() => {
    if (!docId || !inboxId || !pageNo) return;
    
    // Reset states for new image
    setHasImageLoaded(false);
    setIsImageVisible(false);
    
    // Start loading
    if (showLoadingIndicator) {
      if (isDocumentChange) {
        loadingStates.startLoading('document', 'document', 'Loading document...', 2000);
      } else if (isPageChange) {
        loadingStates.startLoading('page', 'page', `Loading page ${pageNo}...`, 1500);
      }
    }
    
    // Load image with thumbnail fallback
    imageLoader.loadImageWithThumbnail(inboxId, docId, pageNo);
    
    // Preload adjacent pages if enabled
    if (enablePreloading && activeDocument) {
      const totalPages = activeDocument.dimensions?.length || 0;
      imageLoader.preloadAdjacentPages(inboxId, docId, pageNo, totalPages);
    }
    
    // Update refs
    previousPageRef.current = pageNo;
    previousDocRef.current = docId;
  }, [docId, inboxId, pageNo, activeDocument, enablePreloading, showLoadingIndicator, imageLoader, loadingStates, isDocumentChange, isPageChange]);
  
  // Handle navigation animations
  useEffect(() => {
    if (isDocumentChange) {
      // Document change animation
      dispatch(setNavigationAnimationState({
        isAnimating: true,
        animationType: 'document',
        direction: 'forward',
        progress: 0,
      }));
      
      navigationAnimations.startAnimation('document', 'forward').then(() => {
        dispatch(setNavigationAnimationState({
          isAnimating: false,
          animationType: 'none',
          direction: 'none',
          progress: 100,
        }));
      });
    } else if (isPageChange) {
      // Page change animation
      const direction = pageNo > previousPageRef.current ? 'forward' : 'backward';
      
      dispatch(setNavigationAnimationState({
        isAnimating: true,
        animationType: 'page',
        direction,
        progress: 0,
      }));
      
      navigationAnimations.startAnimation('page', direction).then(() => {
        dispatch(setNavigationAnimationState({
          isAnimating: false,
          animationType: 'none',
          direction: 'none',
          progress: 100,
        }));
      });
    }
  }, [dispatch, navigationAnimations, isDocumentChange, isPageChange, pageNo]);
  
  // Get animation styles
  const containerAnimationStyles = navigationAnimations.getAnimationStyles('container');
  const imageAnimationStyles = navigationAnimations.getAnimationStyles('image');
  const loadingAnimationStyles = navigationAnimations.getLoadingAnimationStyles(isLoading);
  const growInStyles = navigationAnimations.getGrowInAnimationStyles(isImageVisible, 100);
  
  // Combined loading state
  const combinedLoadingState = loadingStates.getCombinedLoadingState();
  
  return (
    <div
      ref={containerRef}
      className={clsx(styles.container, className, {
        [styles.loading]: isLoading,
        [styles.animating]: navigationAnimationState.isAnimating,
        [styles.showingThumbnail]: isShowingThumbnail,
        [styles.hasError]: hasError,
      })}
      style={{
        ...style,
        ...containerAnimationStyles,
      }}
    >
      {/* Main image display */}
      <NavigationTransition
        isVisible={shouldShowImage}
        type={isDocumentChange ? 'document' : 'page'}
        direction={navigationAnimationState.direction}
        duration={animationDuration}
        className={styles.imageTransition}
      >
        <ImageTransition
          src={displayImageUrl}
          fallbackSrc={thumbUrl}
          alt={`Document ${docId} - Page ${pageNo}`}
          isLoading={isLoading}
          hasError={hasError}
          onLoad={handleImageLoad}
          onError={handleImageError}
          className={styles.image}
          style={{
            ...imageAnimationStyles,
            ...loadingAnimationStyles,
            ...growInStyles,
          }}
        />
      </NavigationTransition>
      
      {/* Loading overlay */}
      {showLoadingIndicator && (
        <LoadingOverlay
          isVisible={combinedLoadingState.isLoading}
          progress={combinedLoadingState.progress}
          type={isLoading && combinedLoadingState.progress > 0 ? 'progress' : 'spinner'}
          message={combinedLoadingState.messages[0]}
          className={styles.loadingOverlay}
        />
      )}
      
      {/* Thumbnail quality indicator */}
      {isShowingThumbnail && shouldShowImage && (
        <div className={styles.thumbnailIndicator}>
          <span>Loading full quality...</span>
        </div>
      )}
      
      {/* Error state */}
      {hasError && (
        <div className={styles.errorState}>
          <div className={styles.errorIcon}>⚠️</div>
          <p>Failed to load image</p>
          <button
            onClick={() => imageLoader.loadImageWithThumbnail(inboxId, docId, pageNo)}
            className={styles.retryButton}
          >
            Retry
          </button>
        </div>
      )}
    </div>
  );
};
