@import '@shared/styles/base';

.container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: #f8f9fa;
  border-radius: 4px;
  
  &.loading {
    .image {
      opacity: 0.7;
      transform: scale(0.98);
    }
  }
  
  &.animating {
    .image {
      transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
    }
  }
  
  &.showingThumbnail {
    .thumbnailIndicator {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  &.hasError {
    background: #fff5f5;
    border: 1px dashed #e53e3e;
  }
}

.imageTransition {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  transition: 
    opacity 300ms cubic-bezier(0.4, 0, 0.2, 1),
    transform 300ms cubic-bezier(0.4, 0, 0.2, 1),
    filter 200ms ease-out;
  will-change: opacity, transform;
  
  &:hover {
    filter: brightness(1.02);
  }
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(2px);
  z-index: 10;
}

.thumbnailIndicator {
  position: absolute;
  bottom: 12px;
  left: 12px;
  padding: 6px 12px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 12px;
  border-radius: 16px;
  opacity: 0;
  transform: translateY(10px);
  transition: 
    opacity 300ms ease-out,
    transform 300ms ease-out;
  z-index: 5;
  
  span {
    display: flex;
    align-items: center;
    gap: 6px;
    
    &::before {
      content: '';
      width: 8px;
      height: 8px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-top: 1px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

.errorState {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 24px;
  text-align: center;
  z-index: 5;
}

.errorIcon {
  font-size: 32px;
  opacity: 0.6;
}

.errorState p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.retryButton {
  padding: 8px 16px;
  background: #0066ff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background 200ms ease-out;
  
  &:hover {
    background: #0052cc;
  }
  
  &:active {
    transform: translateY(1px);
  }
}

// Animation keyframes
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// Page transition animations
.pageTransition {
  &Enter {
    opacity: 0;
    transform: translateX(10px) scale(0.98);
  }
  
  &EnterActive {
    opacity: 1;
    transform: translateX(0) scale(1);
    transition: 
      opacity 300ms cubic-bezier(0.4, 0, 0.2, 1),
      transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  &Exit {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
  
  &ExitActive {
    opacity: 0;
    transform: translateX(-10px) scale(0.98);
    transition: 
      opacity 300ms cubic-bezier(0.4, 0, 0.2, 1),
      transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }
}

// Document transition animations (more dramatic)
.documentTransition {
  &Enter {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  
  &EnterActive {
    opacity: 1;
    transform: translateY(0) scale(1);
    transition: 
      opacity 400ms cubic-bezier(0.25, 0.8, 0.5, 1),
      transform 400ms cubic-bezier(0.25, 0.8, 0.5, 1);
  }
  
  &Exit {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  
  &ExitActive {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
    transition: 
      opacity 400ms cubic-bezier(0.25, 0.8, 0.5, 1),
      transform 400ms cubic-bezier(0.25, 0.8, 0.5, 1);
  }
}

// Responsive design
@media (max-width: 768px) {
  .thumbnailIndicator {
    bottom: 8px;
    left: 8px;
    padding: 4px 8px;
    font-size: 11px;
  }
  
  .errorState {
    padding: 16px;
  }
  
  .errorIcon {
    font-size: 24px;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .container {
    border: 2px solid #000;
    
    &.hasError {
      border-color: #e53e3e;
      background: #fff;
    }
  }
  
  .thumbnailIndicator {
    background: #000;
    border: 1px solid #fff;
  }
  
  .loadingOverlay {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: none;
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .image,
  .thumbnailIndicator,
  .container,
  .pageTransitionEnter,
  .pageTransitionEnterActive,
  .pageTransitionExit,
  .pageTransitionExitActive,
  .documentTransitionEnter,
  .documentTransitionEnterActive,
  .documentTransitionExit,
  .documentTransitionExitActive {
    transition: none !important;
    animation: none !important;
  }
  
  .thumbnailIndicator span::before {
    animation: none !important;
  }
}
