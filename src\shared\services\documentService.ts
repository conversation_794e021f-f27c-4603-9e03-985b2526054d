// documentService.ts
import apiClient from '@shared/helpers/apiClient.ts';
import { IDocument, IDocumentValueLocation } from '@shared/helpers/converters/document.ts';
import {
  IClientMasterdataSearchResult,
  IRawMasterdataSearchResult,
  masterdataResultClientToRaw,
  masterdataResultRawToClient,
} from '@shared/helpers/converters/masterdata-result.ts';
import { firestoreApi } from '@shared/helpers/rtk-query/firestoreApi.ts';
import { documentListSlice } from '@shared/store/documentListSlice.ts';
import { realtime } from '@shared/store/setup/firebase-setup.ts';
import { AppThunk } from '@shared/store/store.ts';
import { AxiosError, AxiosResponse } from 'axios';
import { child, onDisconnect, ref, runTransaction, update } from 'firebase/database';

export interface IDocumentEntityPostPayload {
  type: string;
  value: any;
  rawValue: any;
  categoryId?: string;
  pageNo: number;
  valueLocations: IDocumentValueLocation[];
}

export const addEntityToDocument = async (
  inboxId: string,
  documentId: string,
  entityId: string,
  entityData: IDocumentEntityPostPayload,
  mutationId?: string,
): Promise<void> => {
  let url = `${import.meta.env.VITE_PAPERBOX_BACKEND_URL}/inboxes/${inboxId}/documents/${documentId}`;
  if (mutationId) url += `/mutations/${mutationId}`;
  url += `/entities/${entityId}`;
  return apiClient.put(url, entityData);
};
export interface IDocumentEntityPatchPayload {
  value?: any;
  valueLocations?: IDocumentValueLocation[];
}

export const editEntityInDocument = async (
  inboxId: string,
  documentId: string,
  entityId: string,
  entityData: IDocumentEntityPatchPayload,
  mutationId?: string,
): Promise<void> => {
  let url = `${import.meta.env.VITE_PAPERBOX_BACKEND_URL}/inboxes/${inboxId}/documents/${documentId}`;
  if (mutationId) url += `/mutations/${mutationId}`;
  url += `/entities/${entityId}`;
  return apiClient.patch(url, entityData);
};

export const removeEntityFromDocument = async (
  inboxId: string,
  documentId: string,
  entityId: string,
  mutationId?: string,
): Promise<void> => {
  let url = `${import.meta.env.VITE_PAPERBOX_BACKEND_URL}/inboxes/${inboxId}/documents/${documentId}`;
  if (mutationId) url += `/mutations/${mutationId}`;
  url += `/entities/${entityId}`;

  return apiClient.delete(url);
};
export const deleteDocument = async (
  inboxId: string,
  documentId: string,
  userEmail: string,
): Promise<void> => {
  return apiClient.patch(
    `${import.meta.env.VITE_PAPERBOX_BACKEND_URL}/inboxes/${inboxId}/documents/${documentId}`,
    {
      action: { actor_email: userEmail, type: 'delete' },
    },
  );
};
export const deleteMutation = async (
  inboxId: string,
  documentId: string,
  mutationId: string,
): Promise<void> => {
  return apiClient.delete(
    `${import.meta.env.VITE_PAPERBOX_BACKEND_URL}/inboxes/${inboxId}/documents/${documentId}/mutations/${mutationId}`,
  );
};
export const changeDocumentDoctype = async (
  inboxId: string,
  documentId: string,
  doctypeId: string,
  subtypeId?: string,
  mutationId?: string,
  partId?: string,
): Promise<void> => {
  let url = `${import.meta.env.VITE_PAPERBOX_BACKEND_URL}/inboxes/${inboxId}/documents/${documentId}`;
  if (mutationId) url += `/mutations/${mutationId}`;
  if (partId) url += `/topology/parts/${partId}`;

  return apiClient.patch(url, { doc_type_id: doctypeId, doc_subtype_id: subtypeId });
};

export const patchDocument = async (
  inboxId: string,
  payload: Record<string, any>,
  documentId: string,
  mutationId?: string,
) => {
  let url = `${import.meta.env.VITE_PAPERBOX_BACKEND_URL}/inboxes/${inboxId}/documents/${documentId}`;
  if (mutationId) url += `/mutations/${mutationId}`;
  return apiClient.patch(url, payload);
};

export type TPatchTopologyPartPayload = {
  inboxId: string;
  documentId: string;
  mutationId?: string;
  partId?: string;
  payload: Record<string, any>;
};
export const patchTopologyPart = async ({
  inboxId,
  documentId,
  mutationId,
  partId,
  payload,
}: TPatchTopologyPartPayload): Promise<void> => {
  let url = `${import.meta.env.VITE_PAPERBOX_BACKEND_URL}/inboxes/${inboxId}/documents/${documentId}`;
  if (mutationId) url += `/mutations/${mutationId}`;
  if (partId) url += `/topology/parts/${partId}`;

  return apiClient.patch(url, payload);
};

export const changeDocumentInbox = async (
  documentId: string,
  inboxId: string,
  newInboxId: string,
): Promise<void> => {
  const url = `${import.meta.env.VITE_PAPERBOX_BACKEND_URL}/inboxes/${inboxId}/documents/${documentId}?dest_inbox_id=${newInboxId}`;
  return apiClient.put(url, null);
};

export const createDocumentMutation = async (inboxId: string, docId: string): Promise<AxiosResponse> => {
  return apiClient.post(
    `${import.meta.env.VITE_PAPERBOX_BACKEND_URL}/inboxes/${inboxId}/documents/${docId}/mutations`,
    null,
  );
};

export const bounceDocument = async (
  payload: Record<string, any>,
  actorEmail: string,
  inboxId: string,
  documentId: string,
  mutationId?: string,
): Promise<void> => {
  const list = [];
  Object.entries(payload).forEach(([k, v]) => {
    if (Array.isArray(v)) {
      v.forEach((av) => {
        list.push({ type: k, value: `${av}` });
      });
    } else {
      list.push({ type: k, value: `${v}` });
    }
  });

  const action = { actor_email: actorEmail, type: 'bounce' };
  if (list.length !== 0) action['metadata'] = list;
  let url = `${import.meta.env.VITE_PAPERBOX_BACKEND_URL}/inboxes/${inboxId}/documents/${documentId}`;
  if (mutationId) url += `/mutations/${mutationId}`;

  return apiClient.patch(url, { action: action });
};

export let realtimeLockInterval: NodeJS.Timeout;

export const lockDocument = async (
  tenantId: string,
  inboxId: string,
  documentId: string,
  email: string,
  isHistorical: boolean = false
) => {
  // Prevent locking historical documents
  if (isHistorical) {
    console.warn('Attempted to lock historical document:', documentId);
    return Promise.resolve();
  }

  if (realtimeLockInterval) clearInterval(realtimeLockInterval);
  const lockRef = ref(realtime, `tenants/${tenantId}/inboxes/${inboxId}/lockers`);

  realtimeLockInterval = setInterval(() => {
    const up = {};
    up[documentId] = { email, timestamp: new Date().toISOString() };
    update(lockRef, up);
  }, 30000);

  const deletion = {};
  deletion[documentId] = null;

  // Set the onDisconnect logic
  onDisconnect(lockRef)
    .update(deletion)
    .then(() => {
      // Optionally run a transaction to set initial value on connection
      runTransaction(child(lockRef, documentId), (data) => {
        return data ?? { email, timestamp: new Date().toISOString() };
      });
    })
    .catch((error: Error) => {
      console.error('onDisconnect setup failed:', error);
    });
};
export const unlockDocument = async (tenantId: string, inboxId: string, documentId: string) => {
  if (realtimeLockInterval) clearInterval(realtimeLockInterval);
  const lockRef = ref(realtime, `tenants/${tenantId}/inboxes/${inboxId}/lockers`);
  const deletion = {};
  deletion[documentId] = null;
  return update(lockRef, deletion);
};

/**
 * Clear any existing locks on historical documents
 * Historical documents should never be locked
 */
export const clearHistoricalDocumentLock = async (tenantId: string, inboxId: string, documentId: string) => {
  try {
    const lockRef = ref(realtime, `tenants/${tenantId}/inboxes/${inboxId}/lockers`);
    const deletion = {};
    deletion[documentId] = null;
    await update(lockRef, deletion);
    console.log('Cleared lock for historical document:', documentId);
  } catch (error) {
    console.error('Failed to clear historical document lock:', error);
  }
};

export type MasterDataSearchPayload = {
  prompt: string;
  fields: { prompt: string; reference?: string; reference_type?: string }[];
  table_ids?: string[];
};
export const searchMasterData = async (
  payload: MasterDataSearchPayload,
  inboxId: string,
): Promise<IClientMasterdataSearchResult[]> => {
  const url = `${import.meta.env.VITE_PAPERBOX_MASTERDATA_URL}/inboxes/${inboxId}/search`;
  const response = await apiClient.post(url, payload, {
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
    },
  });
  console.log(response.data);
  // Convert response keys from snake_case to camelCase
  try {
    const results = response.data.results;
    const mapped = results.map((raw) => masterdataResultRawToClient(raw));
    console.log(mapped);
    return mapped;
  } catch (e) {
    console.log(e);
    throw new Error('Error converting master data result');
  }

  // return camelcaseKeys(response.data, { deep: true });
};
// Masterdata import function: calls the API to import a search result
export const importMasterDataResult = async (
  inboxId: string,
  result: IClientMasterdataSearchResult,
  documentId: string,
  mutationId?: string,
): Promise<void> => {
  // Convert the search result from camelCase to snake_case if needed by the backend
  const mapped: IRawMasterdataSearchResult = masterdataResultClientToRaw(result);
  let url = `${import.meta.env.VITE_PAPERBOX_MASTERDATA_URL}/inboxes/${inboxId}/documents/${documentId}`;
  url = mutationId ? `${url}/mutations/${mutationId}/import` : `${url}/import`;
  await apiClient.post(url, mapped, {
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
    },
  });
};

export const getRawPDF = (inboxId: string, documentId: string, mutationId?: string, topologyId?: string) => {
  let url = `${import.meta.env.VITE_PAPERBOX_BACKEND_URL}/inboxes/${inboxId}/documents/${documentId}`;
  if (mutationId) url += `/mutations/${mutationId}`;
  if (topologyId) url += `/topology/${topologyId}`;
  url += '/raw';

  return apiClient.get(url, {
    responseType: 'blob',
  });
};

let thumbController: AbortController;
const BATCH_SIZE = 10; // Thumbnails are fetched in batches

export const getPageImageThumbs =
  (inboxId: string, docId: string): AppThunk =>
  async (dispatch, getState) => {
    console.log(`Loading thumbnails for document: ${docId} in inbox: ${inboxId}`);

    // Get the active document
    const st = firestoreApi.endpoints.getDocument.select({ docId });
    const activeDocument = st(getState()).data as IDocument;
    if (!activeDocument) {
      console.log('No active document found, cannot load thumbnails');
      dispatch(documentListSlice.actions.setIsThumbsLoading(false));
      return;
    }

    // Cancel any previous thumbnail loading
    if (thumbController) {
      thumbController.abort();
    }
    thumbController = new AbortController();
    dispatch(documentListSlice.actions.setIsThumbsLoading(true));

    // Get all page numbers from the document
    const pageNos = [
      ...new Set(
        activeDocument.topology.parts.flatMap((part) => part.pages.map((page) => page.bundlePageNo)),
      ),
    ];

    // Check which thumbnails are already loaded
    const pageImagesMap = getState().documentList.pageImagesMap;
    const missingThumbs = pageNos.filter((pageNo) => {
      return !pageImagesMap || !pageImagesMap[docId] || !pageImagesMap[docId][pageNo]?.thumbUrl;
    });

    if (missingThumbs.length === 0) {
      console.log('All thumbnails already loaded');
      dispatch(documentListSlice.actions.setIsThumbsLoading(false));
      return;
    }

    console.log(`Loading ${missingThumbs.length} thumbnails for pages:`, missingThumbs);
    // Function to process thumbnails in batches
    const fetchThumbnailsInBatches = async (pageNosBatch) => {
      // Create a list of promises for each thumbnail request
      const promiseList = pageNosBatch
        .map((pageNo) => {
          const url = `${
            import.meta.env.VITE_PAPERBOX_CDN_URL
          }/inboxes/${inboxId}/documents/${docId}/pages/${pageNo}?w=490&q=30`;

          return apiClient
            .get(url, {
              signal: thumbController.signal,
              responseType: 'blob',
              headers: {
                accept: 'image/*',
              },
            })
            .then((res) => ({ res, pageNo }))
            .catch((error) => {
              console.error(`Error loading thumbnail for page ${pageNo}:`, error);
              return null;
            });
        })
        .filter(Boolean);

      // Process all thumbnail requests
      const results = await Promise.allSettled(promiseList);

      // Convert blobs to base64 and store in Redux
      results.forEach((result) => {
        if (result.status === 'fulfilled' && result.value?.res?.data) {
          const { res, pageNo } = result.value;
          const reader = new FileReader();
          reader.readAsDataURL(res.data); // Convert Blob to base64
          reader.onloadend = () => {
            const base64String = reader.result as string;
            dispatch(
              documentListSlice.actions.setPageImagesItem({
                docId,
                pageNo,
                thumbUrl: base64String,
              }),
            );
          };
        }
      });
    };

    // Process thumbnails in batches
    for (let i = 0; i < missingThumbs.length; i += BATCH_SIZE) {
      const pageNosBatch = missingThumbs.slice(i, i + BATCH_SIZE);
      await fetchThumbnailsInBatches(pageNosBatch);
    }

    // Signal that all thumbnails are loaded
    dispatch(documentListSlice.actions.setIsThumbsLoading(false));
    console.log('Thumbnail loading complete');
  };

let pageRetryCount = 0;
let imageController: AbortController;
export const getPageImage =
  (inboxId: string, docId: string, pageNo: number): AppThunk =>
  async (dispatch, getState) => {
    const pageImagesMap = getState().documentList.pageImagesMap;
    if (pageImagesMap?.[docId]?.[pageNo]?.imageUrl) return;
    const imageLoading = getState().documentList.isImageLoading;

    console.log('Getting image');

    if (imageLoading && imageController) {
      imageController.abort();
    }
    imageController = new AbortController();

    dispatch(documentListSlice.actions.setIsImageLoading(true));
    const url = `${import.meta.env.VITE_PAPERBOX_CDN_URL}/inboxes/${inboxId}/documents/${docId}/pages/${pageNo}?w=2000`;
    await apiClient
      .get(url, {
        signal: imageController.signal,
        responseType: 'blob',
        headers: {
          accept: 'image/*',
        },
      })
      .then((res) => {
        pageRetryCount = 0;
        // const activeDocId = getState().document.activeDocId;
        // if (activeDocId === docId) {
        const reader = new FileReader();
        reader.readAsDataURL(res.data); // Convert Blob to base64 string
        reader.onloadend = () => {
          const base64String = reader.result as string;
          console.log(`Full image loaded for page ${pageNo}`);

          dispatch(documentListSlice.actions.setPageImagesItem({ docId, pageNo, imageUrl: base64String }));
          dispatch(documentListSlice.actions.setIsImageLoading(false));
        };
        reader.onerror = (error) => {
          console.error(`Error converting image for page ${pageNo}:`, error);
          dispatch(documentListSlice.actions.setIsImageLoading(false));
        };
        // }
      })
      .catch((err: AxiosError) => {
        console.error(`Error loading image for page ${pageNo}:`, err);
        if (pageRetryCount === 0 && (err?.response?.status === 503 || err?.response?.status === 500)) {
          console.log(`Retrying image load for page ${pageNo}`);
          pageRetryCount = 1;
          dispatch(getPageImage(inboxId, docId, pageNo));
        }
        dispatch(documentListSlice.actions.setIsImageLoading(false));
      });
  };

export const validateDocument = async (inboxId: string, documentId: string, mutationId?: string) => {
  let url = `${import.meta.env.VITE_PAPERBOX_BACKEND_URL}/inboxes/${inboxId}/documents/${documentId}`;
  if (mutationId) url += `/mutations/${mutationId}`;
  url += '/validate';
  return apiClient.post(url, null);
};

export const unlinkDocument = async (inboxId: string, documentId: string, mutationId?: string) => {
  let url = `${import.meta.env.VITE_PAPERBOX_MASTERDATA_URL}/inboxes/${inboxId}/documents/${documentId}`;
  if (mutationId) url += `/mutations/${mutationId}`;
  url += '/unlink';
  return apiClient.post(url, null);
};
