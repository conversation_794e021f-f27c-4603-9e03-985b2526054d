import { useCallback, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@shared/store/store';
import { useImageLoader } from './useImageLoader';

export interface PreloadingStrategy {
  adjacentPages: number; // Number of pages to preload before/after current
  documentSiblings: boolean; // Whether to preload next/previous documents
  priorityThreshold: number; // Distance threshold for high priority loading
  maxConcurrentLoads: number; // Maximum concurrent preload requests
  enableAdaptiveStrategy: boolean; // Adapt strategy based on connection speed
}

export interface UseIntelligentPreloadingOptions {
  strategy?: Partial<PreloadingStrategy>;
  enableConnectionAware?: boolean;
  enableUserBehaviorTracking?: boolean;
}

const DEFAULT_STRATEGY: PreloadingStrategy = {
  adjacentPages: 3,
  documentSiblings: true,
  priorityThreshold: 2,
  maxConcurrentLoads: 4,
  enableAdaptiveStrategy: true,
};

export const useIntelligentPreloading = (options: UseIntelligentPreloadingOptions = {}) => {
  const strategy = { ...DEFAULT_STRATEGY, ...options.strategy };
  
  // Refs for tracking
  const preloadQueueRef = useRef<Set<string>>(new Set());
  const loadingCountRef = useRef<number>(0);
  const navigationHistoryRef = useRef<Array<{ docId: string; pageNo: number; timestamp: number }>>([]);
  const connectionSpeedRef = useRef<'slow' | 'fast' | 'unknown'>('unknown');
  
  // Redux selectors
  const activeDocument = useSelector((state: RootState) => state.document.activeDocument);
  const activePageNo = useSelector((state: RootState) => state.document.activePageNo);
  const nextSiblingId = useSelector((state: RootState) => state.document.nextSiblingId);
  const prevSiblingId = useSelector((state: RootState) => state.document.prevSiblingId);
  const inboxId = useSelector((state: RootState) => state.document.inboxId);
  const pageImagesMap = useSelector((state: RootState) => state.document.pageImagesMap);
  
  // Image loader hook
  const imageLoader = useImageLoader({
    enablePreloading: true,
    preloadRange: strategy.adjacentPages,
  });
  
  // Detect connection speed
  const detectConnectionSpeed = useCallback(() => {
    if (!options.enableConnectionAware) return;
    
    // Use Network Information API if available
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      if (connection) {
        const effectiveType = connection.effectiveType;
        connectionSpeedRef.current = ['slow-2g', '2g', '3g'].includes(effectiveType) ? 'slow' : 'fast';
      }
    }
    
    // Fallback: measure image load time
    const testImage = new Image();
    const startTime = Date.now();
    
    testImage.onload = () => {
      const loadTime = Date.now() - startTime;
      connectionSpeedRef.current = loadTime > 1000 ? 'slow' : 'fast';
    };
    
    // Use a small test image
    testImage.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
  }, [options.enableConnectionAware]);
  
  // Track navigation patterns
  const trackNavigation = useCallback((docId: string, pageNo: number) => {
    if (!options.enableUserBehaviorTracking) return;
    
    navigationHistoryRef.current.push({
      docId,
      pageNo,
      timestamp: Date.now(),
    });
    
    // Keep only recent history (last 50 navigations)
    if (navigationHistoryRef.current.length > 50) {
      navigationHistoryRef.current = navigationHistoryRef.current.slice(-50);
    }
  }, [options.enableUserBehaviorTracking]);
  
  // Predict next likely pages based on navigation history
  const predictNextPages = useCallback((currentDocId: string, currentPageNo: number): number[] => {
    if (!options.enableUserBehaviorTracking) return [];
    
    const history = navigationHistoryRef.current;
    const recentHistory = history.slice(-10); // Look at last 10 navigations
    
    // Find patterns in page navigation
    const pageTransitions: Record<number, number[]> = {};
    
    for (let i = 0; i < recentHistory.length - 1; i++) {
      const current = recentHistory[i];
      const next = recentHistory[i + 1];
      
      if (current.docId === currentDocId && next.docId === currentDocId) {
        if (!pageTransitions[current.pageNo]) {
          pageTransitions[current.pageNo] = [];
        }
        pageTransitions[current.pageNo].push(next.pageNo);
      }
    }
    
    // Get most likely next pages
    const likelyPages = pageTransitions[currentPageNo] || [];
    return [...new Set(likelyPages)].slice(0, 3); // Top 3 unique predictions
  }, [options.enableUserBehaviorTracking]);
  
  // Get adaptive strategy based on connection speed
  const getAdaptiveStrategy = useCallback((): PreloadingStrategy => {
    if (!strategy.enableAdaptiveStrategy) return strategy;
    
    const speed = connectionSpeedRef.current;
    
    if (speed === 'slow') {
      return {
        ...strategy,
        adjacentPages: Math.max(1, Math.floor(strategy.adjacentPages / 2)),
        maxConcurrentLoads: Math.max(1, Math.floor(strategy.maxConcurrentLoads / 2)),
        documentSiblings: false,
      };
    }
    
    return strategy;
  }, [strategy]);
  
  // Check if image is already loaded or loading
  const isImageAvailable = useCallback((docId: string, pageNo: number, type: 'thumb' | 'full') => {
    const imageData = pageImagesMap?.[docId]?.[pageNo];
    if (type === 'thumb') {
      return !!imageData?.thumbUrl;
    }
    return !!imageData?.imageUrl;
  }, [pageImagesMap]);
  
  // Generate preload queue
  const generatePreloadQueue = useCallback((docId: string, pageNo: number, totalPages: number) => {
    const adaptiveStrategy = getAdaptiveStrategy();
    const queue: Array<{ docId: string; pageNo: number; type: 'thumb' | 'full'; priority: number }> = [];
    
    // Current page (highest priority)
    if (!isImageAvailable(docId, pageNo, 'full')) {
      queue.push({ docId, pageNo, type: 'full', priority: 1 });
    }
    if (!isImageAvailable(docId, pageNo, 'thumb')) {
      queue.push({ docId, pageNo, type: 'thumb', priority: 1 });
    }
    
    // Adjacent pages
    for (let i = 1; i <= adaptiveStrategy.adjacentPages; i++) {
      const prevPage = pageNo - i;
      const nextPage = pageNo + i;
      
      const priority = i <= adaptiveStrategy.priorityThreshold ? 2 : 3;
      
      // Previous pages
      if (prevPage >= 1) {
        if (!isImageAvailable(docId, prevPage, 'thumb')) {
          queue.push({ docId, pageNo: prevPage, type: 'thumb', priority });
        }
        if (i <= 1 && !isImageAvailable(docId, prevPage, 'full')) {
          queue.push({ docId, pageNo: prevPage, type: 'full', priority: priority + 1 });
        }
      }
      
      // Next pages
      if (nextPage <= totalPages) {
        if (!isImageAvailable(docId, nextPage, 'thumb')) {
          queue.push({ docId, pageNo: nextPage, type: 'thumb', priority });
        }
        if (i <= 1 && !isImageAvailable(docId, nextPage, 'full')) {
          queue.push({ docId, pageNo: nextPage, type: 'full', priority: priority + 1 });
        }
      }
    }
    
    // Predicted pages based on user behavior
    const predictedPages = predictNextPages(docId, pageNo);
    predictedPages.forEach(predictedPageNo => {
      if (predictedPageNo >= 1 && predictedPageNo <= totalPages) {
        if (!isImageAvailable(docId, predictedPageNo, 'thumb')) {
          queue.push({ docId, pageNo: predictedPageNo, type: 'thumb', priority: 2 });
        }
      }
    });
    
    // Sort by priority (lower number = higher priority)
    return queue.sort((a, b) => a.priority - b.priority);
  }, [getAdaptiveStrategy, isImageAvailable, predictNextPages]);
  
  // Execute preloading queue
  const executePreloadQueue = useCallback(async (queue: Array<{ docId: string; pageNo: number; type: 'thumb' | 'full'; priority: number }>) => {
    if (!inboxId) return;
    
    const adaptiveStrategy = getAdaptiveStrategy();
    
    for (const item of queue) {
      // Respect concurrent load limit
      if (loadingCountRef.current >= adaptiveStrategy.maxConcurrentLoads) {
        break;
      }
      
      const queueKey = `${item.docId}-${item.pageNo}-${item.type}`;
      
      // Skip if already in queue
      if (preloadQueueRef.current.has(queueKey)) {
        continue;
      }
      
      preloadQueueRef.current.add(queueKey);
      loadingCountRef.current++;
      
      // Load image with appropriate priority
      const priority = item.priority <= 2 ? 'normal' : 'low';
      
      imageLoader.loadImage(inboxId, item.docId, item.pageNo, item.type, priority)
        .finally(() => {
          preloadQueueRef.current.delete(queueKey);
          loadingCountRef.current--;
        });
    }
  }, [inboxId, getAdaptiveStrategy, imageLoader]);
  
  // Main preloading function
  const preloadForCurrentPage = useCallback(() => {
    if (!activeDocument || !inboxId || !activePageNo) return;
    
    const totalPages = activeDocument.dimensions?.length || 0;
    if (totalPages === 0) return;
    
    // Track navigation
    trackNavigation(activeDocument.id, activePageNo);
    
    // Generate and execute preload queue
    const queue = generatePreloadQueue(activeDocument.id, activePageNo, totalPages);
    executePreloadQueue(queue);
  }, [activeDocument, inboxId, activePageNo, trackNavigation, generatePreloadQueue, executePreloadQueue]);
  
  // Initialize connection detection
  useEffect(() => {
    detectConnectionSpeed();
  }, [detectConnectionSpeed]);
  
  // Preload when page or document changes
  useEffect(() => {
    preloadForCurrentPage();
  }, [preloadForCurrentPage]);
  
  // Clear queue when component unmounts
  useEffect(() => {
    return () => {
      preloadQueueRef.current.clear();
      loadingCountRef.current = 0;
    };
  }, []);
  
  return {
    preloadForCurrentPage,
    connectionSpeed: connectionSpeedRef.current,
    queueSize: preloadQueueRef.current.size,
    activeLoads: loadingCountRef.current,
    navigationHistory: navigationHistoryRef.current,
  };
};
