import { useCallback, useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@shared/store/store';

export interface LoadingState {
  isLoading: boolean;
  loadingType: 'initial' | 'page' | 'document' | 'thumbnail' | 'none';
  progress: number;
  message?: string;
  startTime?: number;
  estimatedDuration?: number;
}

export interface LoadingStates {
  document: LoadingState;
  page: LoadingState;
  thumbnail: LoadingState;
  navigation: LoadingState;
}

export interface UseLoadingStatesOptions {
  minLoadingDuration?: number; // Minimum time to show loading (prevents flashing)
  maxLoadingDuration?: number; // Maximum time before showing error
  showProgressAfter?: number; // Show progress indicator after this delay
  enableEstimatedTime?: boolean;
}

const DEFAULT_OPTIONS: UseLoadingStatesOptions = {
  minLoadingDuration: 200,
  maxLoadingDuration: 30000,
  showProgressAfter: 500,
  enableEstimatedTime: true,
};

const createInitialLoadingState = (): LoadingState => ({
  isLoading: false,
  loadingType: 'none',
  progress: 0,
  message: undefined,
  startTime: undefined,
  estimatedDuration: undefined,
});

export const useLoadingStates = (options: UseLoadingStatesOptions = {}) => {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  // Loading states for different operations
  const [loadingStates, setLoadingStates] = useState<LoadingStates>({
    document: createInitialLoadingState(),
    page: createInitialLoadingState(),
    thumbnail: createInitialLoadingState(),
    navigation: createInitialLoadingState(),
  });
  
  // Refs for timers and cleanup
  const timersRef = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const progressIntervalsRef = useRef<Map<string, NodeJS.Timeout>>(new Map());
  
  // Redux selectors
  const activeDocument = useSelector((state: RootState) => state.document.activeDocument);
  const activePageNo = useSelector((state: RootState) => state.document.activePageNo);
  const isViewerLoaded = useSelector((state: RootState) => state.document.isViewerLoaded);
  const pageImagesMap = useSelector((state: RootState) => state.document.pageImagesMap);
  
  // Clear all timers
  const clearTimers = useCallback(() => {
    timersRef.current.forEach(timer => clearTimeout(timer));
    timersRef.current.clear();
    
    progressIntervalsRef.current.forEach(interval => clearInterval(interval));
    progressIntervalsRef.current.clear();
  }, []);
  
  // Update loading state
  const updateLoadingState = useCallback((
    type: keyof LoadingStates,
    updates: Partial<LoadingState>
  ) => {
    setLoadingStates(prev => ({
      ...prev,
      [type]: { ...prev[type], ...updates }
    }));
  }, []);
  
  // Start loading with optional progress tracking
  const startLoading = useCallback((
    type: keyof LoadingStates,
    loadingType: LoadingState['loadingType'],
    message?: string,
    estimatedDuration?: number
  ) => {
    const startTime = Date.now();
    
    // Clear any existing timers for this type
    const existingTimer = timersRef.current.get(type);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }
    
    const existingInterval = progressIntervalsRef.current.get(type);
    if (existingInterval) {
      clearInterval(existingInterval);
    }
    
    // Update state
    updateLoadingState(type, {
      isLoading: true,
      loadingType,
      progress: 0,
      message,
      startTime,
      estimatedDuration,
    });
    
    // Start progress tracking if estimated duration is provided
    if (estimatedDuration && opts.enableEstimatedTime) {
      const progressInterval = setInterval(() => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min((elapsed / estimatedDuration) * 100, 95); // Cap at 95% until completion
        
        updateLoadingState(type, { progress });
        
        if (progress >= 95) {
          clearInterval(progressInterval);
          progressIntervalsRef.current.delete(type);
        }
      }, 100);
      
      progressIntervalsRef.current.set(type, progressInterval);
    }
    
    // Set maximum loading timeout
    const maxTimer = setTimeout(() => {
      console.warn(`Loading timeout for ${type} after ${opts.maxLoadingDuration}ms`);
      stopLoading(type, false);
    }, opts.maxLoadingDuration);
    
    timersRef.current.set(`${type}-max`, maxTimer);
  }, [updateLoadingState, opts.enableEstimatedTime, opts.maxLoadingDuration]);
  
  // Stop loading with minimum duration enforcement
  const stopLoading = useCallback((
    type: keyof LoadingStates,
    success: boolean = true,
    finalMessage?: string
  ) => {
    const currentState = loadingStates[type];
    
    if (!currentState.isLoading) return;
    
    const elapsed = currentState.startTime ? Date.now() - currentState.startTime : 0;
    const remainingMinTime = Math.max(0, opts.minLoadingDuration - elapsed);
    
    const finishLoading = () => {
      // Clear timers
      const maxTimer = timersRef.current.get(`${type}-max`);
      if (maxTimer) {
        clearTimeout(maxTimer);
        timersRef.current.delete(`${type}-max`);
      }
      
      const progressInterval = progressIntervalsRef.current.get(type);
      if (progressInterval) {
        clearInterval(progressInterval);
        progressIntervalsRef.current.delete(type);
      }
      
      // Update final state
      updateLoadingState(type, {
        isLoading: false,
        loadingType: 'none',
        progress: success ? 100 : 0,
        message: finalMessage,
      });
      
      // Clear message after delay
      if (finalMessage) {
        const clearMessageTimer = setTimeout(() => {
          updateLoadingState(type, { message: undefined });
        }, 2000);
        
        timersRef.current.set(`${type}-clear`, clearMessageTimer);
      }
    };
    
    if (remainingMinTime > 0) {
      const minTimer = setTimeout(finishLoading, remainingMinTime);
      timersRef.current.set(`${type}-min`, minTimer);
    } else {
      finishLoading();
    }
  }, [loadingStates, opts.minLoadingDuration, updateLoadingState]);
  
  // Update progress for ongoing loading
  const updateProgress = useCallback((
    type: keyof LoadingStates,
    progress: number,
    message?: string
  ) => {
    updateLoadingState(type, { 
      progress: Math.min(Math.max(progress, 0), 100),
      message 
    });
  }, [updateLoadingState]);
  
  // Get combined loading state
  const getCombinedLoadingState = useCallback(() => {
    const states = Object.values(loadingStates);
    const isAnyLoading = states.some(state => state.isLoading);
    const highestProgress = Math.max(...states.map(state => state.progress));
    const currentMessages = states
      .filter(state => state.message)
      .map(state => state.message)
      .filter(Boolean);
    
    return {
      isLoading: isAnyLoading,
      progress: highestProgress,
      messages: currentMessages,
      activeLoadingTypes: states
        .filter(state => state.isLoading)
        .map(state => state.loadingType),
    };
  }, [loadingStates]);
  
  // Auto-detect loading states from Redux
  useEffect(() => {
    // Document loading
    if (!activeDocument) {
      if (!loadingStates.document.isLoading) {
        startLoading('document', 'document', 'Loading document...', 2000);
      }
    } else {
      if (loadingStates.document.isLoading) {
        stopLoading('document', true, 'Document loaded');
      }
    }
  }, [activeDocument, loadingStates.document.isLoading, startLoading, stopLoading]);
  
  useEffect(() => {
    // Page loading
    if (activeDocument && activePageNo) {
      const pageImage = pageImagesMap?.[activeDocument.id]?.[activePageNo];
      
      if (!pageImage?.imageUrl && !loadingStates.page.isLoading) {
        startLoading('page', 'page', `Loading page ${activePageNo}...`, 1500);
      } else if (pageImage?.imageUrl && loadingStates.page.isLoading) {
        stopLoading('page', true);
      }
    }
  }, [activeDocument, activePageNo, pageImagesMap, loadingStates.page.isLoading, startLoading, stopLoading]);
  
  useEffect(() => {
    // Thumbnail loading
    if (activeDocument) {
      const thumbnails = pageImagesMap?.[activeDocument.id];
      const totalPages = activeDocument.dimensions?.length || 0;
      
      if (totalPages > 0) {
        const loadedThumbnails = thumbnails ? Object.keys(thumbnails).length : 0;
        const progress = (loadedThumbnails / totalPages) * 100;
        
        if (progress < 100 && !loadingStates.thumbnail.isLoading) {
          startLoading('thumbnail', 'thumbnail', 'Loading thumbnails...', 3000);
        } else if (progress >= 100 && loadingStates.thumbnail.isLoading) {
          stopLoading('thumbnail', true, 'Thumbnails loaded');
        } else if (loadingStates.thumbnail.isLoading) {
          updateProgress('thumbnail', progress);
        }
      }
    }
  }, [activeDocument, pageImagesMap, loadingStates.thumbnail.isLoading, startLoading, stopLoading, updateProgress]);
  
  // Cleanup on unmount
  useEffect(() => {
    return clearTimers;
  }, [clearTimers]);
  
  return {
    // Individual loading states
    loadingStates,
    
    // Control functions
    startLoading,
    stopLoading,
    updateProgress,
    
    // State getters
    getCombinedLoadingState,
    
    // Utilities
    clearTimers,
  };
};
