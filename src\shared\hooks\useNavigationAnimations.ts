import { useCallback, useEffect, useRef, useState } from 'react';

export interface AnimationConfig {
  duration: number;
  easing: string;
  delay?: number;
}

export interface NavigationAnimationState {
  isAnimating: boolean;
  animationType: 'page' | 'document' | 'none';
  direction: 'forward' | 'backward' | 'none';
  progress: number;
}

export interface UseNavigationAnimationsOptions {
  pageTransitionDuration?: number;
  documentTransitionDuration?: number;
  defaultEasing?: string;
  enableReducedMotion?: boolean;
}

const DEFAULT_OPTIONS: UseNavigationAnimationsOptions = {
  pageTransitionDuration: 300,
  documentTransitionDuration: 400,
  defaultEasing: 'cubic-bezier(0.4, 0, 0.2, 1)',
  enableReducedMotion: true,
};

export const useNavigationAnimations = (options: UseNavigationAnimationsOptions = {}) => {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  // Animation state
  const [animationState, setAnimationState] = useState<NavigationAnimationState>({
    isAnimating: false,
    animationType: 'none',
    direction: 'none',
    progress: 0,
  });
  
  // Refs for cleanup
  const animationTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  
  // Check for reduced motion preference
  const prefersReducedMotion = useCallback(() => {
    if (!opts.enableReducedMotion) return false;
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }, [opts.enableReducedMotion]);
  
  // Clear all timers and animations
  const clearAnimations = useCallback(() => {
    if (animationTimeoutRef.current) {
      clearTimeout(animationTimeoutRef.current);
      animationTimeoutRef.current = null;
    }
    
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
    
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }
  }, []);
  
  // Start navigation animation
  const startAnimation = useCallback((
    type: 'page' | 'document',
    direction: 'forward' | 'backward' = 'forward'
  ) => {
    // Clear any existing animations
    clearAnimations();
    
    // Check for reduced motion
    if (prefersReducedMotion()) {
      // Skip animation but still trigger state changes
      setAnimationState({
        isAnimating: false,
        animationType: type,
        direction,
        progress: 100,
      });
      return Promise.resolve();
    }
    
    const duration = type === 'page' ? opts.pageTransitionDuration : opts.documentTransitionDuration;
    
    return new Promise<void>((resolve) => {
      // Set initial animation state
      setAnimationState({
        isAnimating: true,
        animationType: type,
        direction,
        progress: 0,
      });
      
      // Track progress
      const startTime = Date.now();
      const updateProgress = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min((elapsed / duration) * 100, 100);
        
        setAnimationState(prev => ({
          ...prev,
          progress,
        }));
        
        if (progress < 100) {
          animationFrameRef.current = requestAnimationFrame(updateProgress);
        }
      };
      
      // Start progress tracking
      animationFrameRef.current = requestAnimationFrame(updateProgress);
      
      // Complete animation after duration
      animationTimeoutRef.current = setTimeout(() => {
        clearAnimations();
        setAnimationState({
          isAnimating: false,
          animationType: 'none',
          direction: 'none',
          progress: 100,
        });
        resolve();
      }, duration);
    });
  }, [clearAnimations, prefersReducedMotion, opts.pageTransitionDuration, opts.documentTransitionDuration]);
  
  // Get animation CSS properties
  const getAnimationStyles = useCallback((
    element: 'container' | 'image' | 'thumbnail',
    customConfig?: Partial<AnimationConfig>
  ) => {
    const { isAnimating, animationType, direction, progress } = animationState;
    
    if (!isAnimating && progress === 100) {
      return {};
    }
    
    const duration = animationType === 'page' ? opts.pageTransitionDuration : opts.documentTransitionDuration;
    const config: AnimationConfig = {
      duration,
      easing: opts.defaultEasing,
      delay: 0,
      ...customConfig,
    };
    
    const baseStyles = {
      transition: `all ${config.duration}ms ${config.easing}`,
      transitionDelay: `${config.delay}ms`,
    };
    
    // Element-specific animation styles
    switch (element) {
      case 'container':
        if (isAnimating) {
          return {
            ...baseStyles,
            transform: direction === 'forward' ? 'translateX(-2px)' : 'translateX(2px)',
            opacity: 0.95,
          };
        }
        return {
          ...baseStyles,
          transform: 'translateX(0)',
          opacity: 1,
        };
        
      case 'image':
        if (isAnimating) {
          const scale = animationType === 'document' ? 0.98 : 0.99;
          return {
            ...baseStyles,
            transform: `scale(${scale})`,
            opacity: 0.8,
          };
        }
        return {
          ...baseStyles,
          transform: 'scale(1)',
          opacity: 1,
        };
        
      case 'thumbnail':
        if (isAnimating && animationType === 'page') {
          return {
            ...baseStyles,
            transform: 'scale(1.05)',
            boxShadow: '0 0 20px rgba(0, 102, 255, 0.3)',
          };
        }
        return {
          ...baseStyles,
          transform: 'scale(1)',
          boxShadow: 'none',
        };
        
      default:
        return baseStyles;
    }
  }, [animationState, opts.pageTransitionDuration, opts.documentTransitionDuration, opts.defaultEasing]);
  
  // Get loading animation styles
  const getLoadingAnimationStyles = useCallback((isLoading: boolean, progress: number = 0) => {
    if (!isLoading) {
      return {
        opacity: 1,
        transform: 'scale(1)',
        transition: 'opacity 200ms ease-out, transform 200ms ease-out',
      };
    }
    
    return {
      opacity: 0.7,
      transform: 'scale(0.98)',
      transition: 'opacity 200ms ease-in, transform 200ms ease-in',
    };
  }, []);
  
  // Get grow-in animation for newly loaded content
  const getGrowInAnimationStyles = useCallback((isVisible: boolean, delay: number = 0) => {
    return {
      opacity: isVisible ? 1 : 0,
      transform: isVisible ? 'scale(1)' : 'scale(0.95)',
      transition: `opacity 300ms ease-out ${delay}ms, transform 300ms ease-out ${delay}ms`,
    };
  }, []);
  
  // Cleanup on unmount
  useEffect(() => {
    return clearAnimations;
  }, [clearAnimations]);
  
  return {
    // Animation state
    animationState,
    
    // Animation controls
    startAnimation,
    clearAnimations,
    
    // Style generators
    getAnimationStyles,
    getLoadingAnimationStyles,
    getGrowInAnimationStyles,
    
    // Utilities
    prefersReducedMotion,
  };
};
