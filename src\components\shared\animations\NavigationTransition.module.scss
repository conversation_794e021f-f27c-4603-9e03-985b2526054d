@import '@shared/styles/base';

// CSS Custom Properties for dynamic values
:root {
  --transition-duration: 300ms;
  --transition-direction: forward;
}

// Base transition container
.transitionContainer {
  position: relative;
  width: 100%;
  height: 100%;
}

// Page transition animations
.pageEnter {
  opacity: 0;
  transform: translateX(-10px) scale(0.98);
}

.pageEnterActive {
  opacity: 1;
  transform: translateX(0) scale(1);
  transition:
    opacity var(--transition-duration) cubic-bezier(0.4, 0, 0.2, 1),
    transform var(--transition-duration) cubic-bezier(0.4, 0, 0.2, 1);
}

.pageExit {
  opacity: 1;
  transform: translateX(0) scale(1);
}

.pageExitActive {
  opacity: 0;
  transform: translateX(10px) scale(0.98);
  transition:
    opacity var(--transition-duration) cubic-bezier(0.4, 0, 0.2, 1),
    transform var(--transition-duration) cubic-bezier(0.4, 0, 0.2, 1);
}

// Document transition animations (more dramatic)
.documentEnter {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
}

.documentEnterActive {
  opacity: 1;
  transform: translateY(0) scale(1);
  transition:
    opacity calc(var(--transition-duration) * 1.3) cubic-bezier(0.25, 0.8, 0.5, 1),
    transform calc(var(--transition-duration) * 1.3) cubic-bezier(0.25, 0.8, 0.5, 1);
}

.documentExit {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.documentExitActive {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
  transition:
    opacity calc(var(--transition-duration) * 1.3) cubic-bezier(0.25, 0.8, 0.5, 1),
    transform calc(var(--transition-duration) * 1.3) cubic-bezier(0.25, 0.8, 0.5, 1);
}

// Loading overlay animations
.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(2px);
  z-index: 10;
}

.loadingEnter {
  opacity: 0;
}

.loadingEnterActive {
  opacity: 1;
  transition: opacity 200ms ease-out;
}

.loadingExit {
  opacity: 1;
}

.loadingExitActive {
  opacity: 0;
  transition: opacity 200ms ease-in;
}

.loadingContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

// Spinner animation
.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(0, 102, 255, 0.1);
  border-top: 3px solid #0066ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// Progress bar
.progressContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  min-width: 200px;
}

.progressBar {
  width: 100%;
  height: 4px;
  background: rgba(0, 102, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #0066ff, #00aaff);
  border-radius: 2px;
  transition: width 200ms ease-out;
}

.progressText {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

// Skeleton loading
.skeleton {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 200px;
}

.skeletonLine {
  height: 12px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  border-radius: 6px;
  animation: shimmer 1.5s infinite;

  &:nth-child(1) {
    width: 100%;
  }

  &:nth-child(2) {
    width: 80%;
  }

  &:nth-child(3) {
    width: 60%;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

.loadingMessage {
  margin: 0;
  font-size: 14px;
  color: #666;
  text-align: center;
}

// Image transition styles
.imageContainer {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition:
    opacity 300ms cubic-bezier(0.4, 0, 0.2, 1),
    transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
  will-change: opacity, transform;
}

.imageLoaded {
  opacity: 1 !important;
  transform: scale(1) !important;
}

.imagePlaceholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.imageError {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  color: #666;
  font-size: 14px;
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {

  .pageEnter,
  .pageEnterActive,
  .pageExit,
  .pageExitActive,
  .documentEnter,
  .documentEnterActive,
  .documentExit,
  .documentExitActive,
  .image {
    transition: none !important;
    animation: none !important;
  }

  .spinner {
    animation: none !important;
  }

  .skeletonLine {
    animation: none !important;
    background: #e0e0e0 !important;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .loadingOverlay {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: none;
  }

  .spinner {
    border-color: #000;
    border-top-color: #0066ff;
  }

  .progressFill {
    background: #0066ff;
  }
}