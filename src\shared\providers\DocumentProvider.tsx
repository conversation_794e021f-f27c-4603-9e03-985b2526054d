import { setIsThumbsVisible } from '@shared/store/labelerSlice.ts';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router';
import { useNotification } from '../hooks/useNotificationBar';
import {
  initializeDocument,
  setDeletingMutationId,
  setDocumentJSON,
  setHistoricalSiblings,
  setIsDeletingMutation,
  setIsJSONLoading,
  setIsMainDocLoading,
  setLockedDocId,
  setMainDocument,
  setMutations,
  setMutationsLoading,
  setNextSiblingId,
  setPrevSiblingId,
  setSelectedMutationId,
  updateAllLoading,
  updatePageIndexMap,
} from '../store/documentSlice';
import { useDispatch, useSelector } from '../store/store';

import { enrichDocumentWithTypes, sortEntities } from '../helpers/newHelpers';
import { useGetHistoricalSiblingsQuery } from '../helpers/rtk-query/analyticsApi';
import { useGetDocumentJSONQuery } from '../helpers/rtk-query/backendApi';
import {
  useGetDoctypesQuery,
  useGetDocumentMutationsQuery,
  useGetDocumentQuery,
  useGetFieldtypesQuery,
  useGetNextSiblingQuery,
  useGetPrevSiblingQuery,
  useGetTagtypesQuery,
} from '../helpers/rtk-query/firestoreApi';
import {
  getPageImage,
  getPageImageThumbs,
  lockDocument,
  unlockDocument,
  clearHistoricalDocumentLock,
} from '../services/documentService';
import { useIntelligentPreloading } from '../hooks/useIntelligentPreloading';

/**
 * Provider component that handles document-related side effects
 */
export const DocumentProvider: React.FC<{
  children: React.ReactNode;
  mainDocId: string;
  inboxId: string;
  historical: boolean;
}> = ({ children, mainDocId, inboxId, historical }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { showNotification } = useNotification();
  const { t } = useTranslation('document');

  // Get user and tenant info
  const userAccount = useSelector((state) => state.user.userAccount);
  const tenantId = useSelector((state) => state.tenant.tenantId);
  const filters = useSelector((state) => state.documentList.filters);
  const pageImagesMap = useSelector((state) => state.documentList.pageImagesMap);

  // Check if user has access to a specific inbox
  const checkInboxAccess = (targetInboxId: string) => {
    return userAccount?.inboxes?.includes(targetInboxId);
  };

  // Early access check for direct URL protection
  useEffect(() => {
    if (userAccount?.email && inboxId && !checkInboxAccess(inboxId)) {
      // User is trying to access an inbox they don't have permission for
      showNotification(t('routing.accessDenied'), 'error', 3000);
      navigate('/inbox', { replace: true });
    }
  }, [userAccount, inboxId, navigate, showNotification, t]);

  // Initialize document state
  useEffect(() => {
    dispatch(initializeDocument({ mainDocId, inboxId, historical }));
  }, [dispatch, mainDocId, inboxId, historical]);

  // Document data fetching
  const {
    data: docDataR,
    isFetching: mainDocLoading,
    error,
  } = useGetDocumentQuery({ docId: mainDocId }, { skip: !mainDocId });

  const { data: nextSiblingId } = useGetNextSiblingQuery(
    { inboxId, filters, startAfterDocument: docDataR },
    { skip: historical || !docDataR || !filters },
  );

  const { data: prevSiblingId } = useGetPrevSiblingQuery(
    { inboxId, filters, endBeforeDocument: docDataR },
    { skip: historical || !docDataR },
  );

  const { data: historicalSiblings } = useGetHistoricalSiblingsQuery(
    { inboxId, documentId: mainDocId, filters },
    { skip: !historical || !mainDocId },
  );

  const { all: doctypes } = useGetDoctypesQuery({ inboxId }).data ?? {};
  const { data: fieldTypes } = useGetFieldtypesQuery({ inboxId });
  const { data: tagTypes } = useGetTagtypesQuery({ inboxId });
  const { data: documentJSONR, isFetching: jsonLoading } = useGetDocumentJSONQuery({
    inboxId,
    docId: mainDocId,
  });

  const { data: mutations = [], isLoading: mutationsLoading } = useGetDocumentMutationsQuery(
    { docId: mainDocId },
    { skip: !mainDocId },
  );

  // Invalid document ID detection with timeout
  useEffect(() => {
    if (!mainDocId || !userAccount?.email) return;

    // Set a timeout to check if document loaded after reasonable time
    const timeoutId = setTimeout(() => {
      // If after 5 seconds we have no document data and no error, likely invalid ID
      if (!docDataR && !mainDocLoading && !error) {
        console.error(`Invalid document ID detected (timeout): ${mainDocId}`);
        showNotification(t('routing.invalidDocumentId'), 'error', 3000);
        navigate(`/inbox/${inboxId}`, { replace: true });
      }
    }, 5000);

    // Clear timeout if document loads or error occurs
    if (docDataR || error) {
      clearTimeout(timeoutId);
    }

    return () => clearTimeout(timeoutId);
  }, [mainDocId, docDataR, mainDocLoading, error, userAccount, showNotification, t, navigate, inboxId]);

  // Simplified document routing: handle document moves
  useEffect(() => {
    if (docDataR && !mainDocLoading && inboxId && docDataR.inboxId !== inboxId && !historical) {
      const documentInboxId = docDataR.inboxId;

      // User doesn't have access - redirect to document list
      showNotification(t('routing.documentMoved'), 'error', 3000);
      console.error(`Document ${mainDocId} moved to an inaccessible inbox ${documentInboxId}`);
      navigate(`/inbox/${inboxId}`, { replace: true });
    }
  }, [docDataR, mainDocLoading, inboxId, mainDocId, historical, showNotification, t, navigate]);

  // Document status mismatch handling: redirect processed documents to historical view
  useEffect(() => {
    if (docDataR && !mainDocLoading && !historical && location.pathname.includes('/documents/')) {
      // Check if document has been processed
      if (docDataR.action && docDataR.active === false) {
        // Document has been processed, redirect to historical view
        const historicalPath = `/inbox/${inboxId}/historical/${mainDocId}`;
        showNotification(t('routing.documentProcessed'), 'warning', 2000);
        navigate(historicalPath, { replace: true });
      }
    }
  }, [
    docDataR,
    mainDocLoading,
    historical,
    location.pathname,
    inboxId,
    mainDocId,
    showNotification,
    t,
    navigate,
  ]);

  // Update loading states
  useEffect(() => {
    dispatch(setIsMainDocLoading(mainDocLoading));
  }, [dispatch, mainDocLoading]);

  useEffect(() => {
    dispatch(setIsJSONLoading(jsonLoading));
  }, [dispatch, jsonLoading]);

  useEffect(() => {
    dispatch(setMutationsLoading(mutationsLoading));
  }, [dispatch, mutationsLoading]);

  useEffect(() => {
    dispatch(updateAllLoading());
  }, [dispatch, mainDocLoading, jsonLoading, mutationsLoading]);

  // Update document data in Redux store
  useEffect(() => {
    if (!docDataR || !doctypes || !fieldTypes || !tagTypes) return;

    const docCopy = { ...docDataR };
    if (docCopy.entities) docCopy.entities = sortEntities(docCopy, docCopy.entities);
    const doc = enrichDocumentWithTypes(docCopy, doctypes, fieldTypes, tagTypes);

    dispatch(setMainDocument(doc));
    dispatch(updatePageIndexMap());
  }, [dispatch, docDataR, doctypes, fieldTypes, tagTypes]);

  useEffect(() => {
    if (!mutations || !doctypes || !fieldTypes || !tagTypes) return;

    const enrichedMutations = mutations.map((mutation) => {
      const mutationCopy = { ...mutation };
      if (mutationCopy.entities) mutationCopy.entities = sortEntities(mutationCopy, mutationCopy.entities);
      return enrichDocumentWithTypes(mutationCopy, doctypes ?? [], fieldTypes ?? [], tagTypes ?? []);
    });

    dispatch(setMutations(enrichedMutations));
    dispatch(updatePageIndexMap());
  }, [dispatch, mutations, doctypes, fieldTypes, tagTypes]);

  useEffect(() => {
    dispatch(setDocumentJSON(documentJSONR));
  }, [dispatch, documentJSONR]);

  // Update navigation data
  useEffect(() => {
    dispatch(setNextSiblingId(nextSiblingId || null));
  }, [dispatch, nextSiblingId]);

  useEffect(() => {
    dispatch(setPrevSiblingId(prevSiblingId || null));
  }, [dispatch, prevSiblingId]);

  useEffect(() => {
    dispatch(setHistoricalSiblings(historicalSiblings || null));
  }, [dispatch, historicalSiblings]);

  // Document locking side effect
  useEffect(() => {
    if (userAccount.isHidden) return;

    if (mainDocId && userAccount.email) {
      if (historical) {
        // For historical documents, ensure any existing locks are cleared
        clearHistoricalDocumentLock(tenantId, inboxId, mainDocId);
      } else {
        // Only lock non-historical documents
        lockDocument(tenantId, inboxId, mainDocId, userAccount.email, historical).then(() => {
          dispatch(setLockedDocId(mainDocId));
        });
      }
    }

    return () => {
      if (mainDocId && userAccount.email && !historical) {
        unlockDocument(tenantId, inboxId, mainDocId).then(() => {
          console.log('UNLOCKING DOC');
        });
      }
    };
  }, [dispatch, tenantId, mainDocId, inboxId, userAccount, historical]);

  // Get deletion state
  const isDeletingMutation = useSelector((state) => state.document.isDeletingMutation);
  const deletingMutationId = useSelector((state) => state.document.deletingMutationId);

  // Handle mutation deletion completion
  useEffect(() => {
    if (isDeletingMutation && deletingMutationId) {
      const stillExists = mutations.some((m) => m.id === deletingMutationId);
      if (!stillExists) {
        const previousMutation = mutations[mutations.length - 1];
        const newSelected = previousMutation ? previousMutation.id : 'original';
        dispatch(setSelectedMutationId(newSelected));
        dispatch(setIsDeletingMutation(false));
        dispatch(setDeletingMutationId(null));
      }
    }
  }, [dispatch, mutations, isDeletingMutation, deletingMutationId]);

  const activeDocument = useSelector((state) => state.document.activeDocument);
  const selectedMutationId = useSelector((state) => state.document.selectedMutationId);

  // Update document title
  useEffect(() => {
    if (activeDocument) document.title = `${activeDocument.name} | Paperbox`;

    return () => {
      document.title = 'Home | Paperbox';
    };
  }, [activeDocument]);

  // Update page index map when selected mutation changes
  useEffect(() => {
    dispatch(updatePageIndexMap());
  }, [dispatch, selectedMutationId]);

  // Load thumbnails and images
  const isDocumentLoaded = useSelector((state) => state.document.mainDocument !== null);
  const isThumbsLoading = useSelector((state) => state.documentList.isThumbsLoading);
  const isImageLoading = useSelector((state) => state.documentList.isImageLoading);
  const activePageNo = useSelector((state) => state.document.activePageNo);

  // Load thumbnails when document is loaded
  useEffect(() => {
    if (isDocumentLoaded && mainDocId && inboxId && !isThumbsLoading) {
      console.log(`Loading thumbnails for document: ${mainDocId}`);
      dispatch(getPageImageThumbs(inboxId, mainDocId));
    }
  }, [isDocumentLoaded, mainDocId, inboxId, isThumbsLoading, dispatch]);

  // Load current page image when page changes
  useEffect(() => {
    if (isDocumentLoaded && mainDocId && inboxId && activePageNo && !isImageLoading) {
      console.log(`Loading image for page ${activePageNo}`);
      dispatch(getPageImage(inboxId, mainDocId, activePageNo));
    }
  }, [isDocumentLoaded, mainDocId, inboxId, activePageNo, isImageLoading, dispatch]);

  // Update thumbnails visibility
  useEffect(() => {
    if (pageImagesMap?.[mainDocId]) {
      const list = Object.values(pageImagesMap[mainDocId]);
      if (list[0]?.thumbUrl) {
        dispatch(setIsThumbsVisible(true));
      }
    } else {
      dispatch(setIsThumbsVisible(false));
    }
  }, [dispatch, mainDocId, pageImagesMap]);

  // Handle errors with better user feedback
  useEffect(() => {
    if (error) {
      const errorWithStatus = error as { status?: number };
      if (errorWithStatus?.status === 404) {
        showNotification(t('routing.invalidDocumentId'), 'error', 3000);
        navigate(`/inbox/${inboxId}`, { replace: true });
      } else if (errorWithStatus?.status === 403) {
        showNotification(t('routing.accessDenied'), 'error', 3000);
        navigate('/inbox', { replace: true });
      } else {
        showNotification(t('routing.documentNotFound'), 'error', 3000);
        navigate(`/inbox/${inboxId}`, { replace: true });
      }
    }
  }, [error, showNotification, t, navigate, inboxId]);

  return <>{children}</>;
};

export default DocumentProvider;
