import React, { useEffect, useRef } from 'react';
import { CSSTransition } from 'react-transition-group';
import styles from './NavigationTransition.module.scss';

export interface NavigationTransitionProps {
  children: React.ReactNode;
  isVisible: boolean;
  type: 'page' | 'document';
  direction?: 'forward' | 'backward';
  duration?: number;
  onEntered?: () => void;
  onExited?: () => void;
  className?: string;
}

export const NavigationTransition: React.FC<NavigationTransitionProps> = ({
  children,
  isVisible,
  type,
  direction = 'forward',
  duration = 300,
  onEntered,
  onExited,
  className = '',
}) => {
  const nodeRef = useRef<HTMLDivElement>(null);
  
  // Set CSS custom properties for animation
  useEffect(() => {
    if (nodeRef.current) {
      nodeRef.current.style.setProperty('--transition-duration', `${duration}ms`);
      nodeRef.current.style.setProperty('--transition-direction', direction);
    }
  }, [duration, direction]);
  
  const transitionClasses = {
    enter: styles[`${type}Enter`],
    enterActive: styles[`${type}EnterActive`],
    exit: styles[`${type}Exit`],
    exitActive: styles[`${type}ExitActive`],
  };
  
  return (
    <CSSTransition
      nodeRef={nodeRef}
      in={isVisible}
      timeout={duration}
      classNames={transitionClasses}
      onEntered={onEntered}
      onExited={onExited}
      unmountOnExit={false}
    >
      <div ref={nodeRef} className={`${styles.transitionContainer} ${className}`}>
        {children}
      </div>
    </CSSTransition>
  );
};

export interface LoadingOverlayProps {
  isVisible: boolean;
  progress?: number;
  type?: 'spinner' | 'progress' | 'skeleton';
  message?: string;
  className?: string;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isVisible,
  progress = 0,
  type = 'spinner',
  message,
  className = '',
}) => {
  const nodeRef = useRef<HTMLDivElement>(null);
  
  return (
    <CSSTransition
      nodeRef={nodeRef}
      in={isVisible}
      timeout={200}
      classNames={{
        enter: styles.loadingEnter,
        enterActive: styles.loadingEnterActive,
        exit: styles.loadingExit,
        exitActive: styles.loadingExitActive,
      }}
      unmountOnExit
    >
      <div ref={nodeRef} className={`${styles.loadingOverlay} ${className}`}>
        <div className={styles.loadingContent}>
          {type === 'spinner' && (
            <div className={styles.spinner} />
          )}
          
          {type === 'progress' && (
            <div className={styles.progressContainer}>
              <div className={styles.progressBar}>
                <div 
                  className={styles.progressFill}
                  style={{ width: `${progress}%` }}
                />
              </div>
              <span className={styles.progressText}>{Math.round(progress)}%</span>
            </div>
          )}
          
          {type === 'skeleton' && (
            <div className={styles.skeleton}>
              <div className={styles.skeletonLine} />
              <div className={styles.skeletonLine} />
              <div className={styles.skeletonLine} />
            </div>
          )}
          
          {message && (
            <p className={styles.loadingMessage}>{message}</p>
          )}
        </div>
      </div>
    </CSSTransition>
  );
};

export interface ImageTransitionProps {
  src?: string;
  fallbackSrc?: string;
  alt: string;
  isLoading?: boolean;
  hasError?: boolean;
  onLoad?: () => void;
  onError?: () => void;
  className?: string;
  style?: React.CSSProperties;
}

export const ImageTransition: React.FC<ImageTransitionProps> = ({
  src,
  fallbackSrc,
  alt,
  isLoading = false,
  hasError = false,
  onLoad,
  onError,
  className = '',
  style = {},
}) => {
  const [imageLoaded, setImageLoaded] = React.useState(false);
  const [showFallback, setShowFallback] = React.useState(false);
  const imageRef = useRef<HTMLImageElement>(null);
  
  // Reset state when src changes
  useEffect(() => {
    setImageLoaded(false);
    setShowFallback(false);
  }, [src]);
  
  const handleImageLoad = () => {
    setImageLoaded(true);
    setShowFallback(false);
    onLoad?.();
  };
  
  const handleImageError = () => {
    if (fallbackSrc && !showFallback) {
      setShowFallback(true);
    } else {
      onError?.();
    }
  };
  
  const currentSrc = showFallback ? fallbackSrc : src;
  const shouldShowImage = currentSrc && !hasError;
  
  return (
    <div className={`${styles.imageContainer} ${className}`} style={style}>
      {shouldShowImage && (
        <img
          ref={imageRef}
          src={currentSrc}
          alt={alt}
          onLoad={handleImageLoad}
          onError={handleImageError}
          className={`${styles.image} ${imageLoaded ? styles.imageLoaded : ''}`}
          style={{
            opacity: imageLoaded ? 1 : 0,
            transform: imageLoaded ? 'scale(1)' : 'scale(0.95)',
          }}
        />
      )}
      
      {(isLoading || !imageLoaded) && shouldShowImage && (
        <div className={styles.imagePlaceholder}>
          <div className={styles.spinner} />
        </div>
      )}
      
      {hasError && (
        <div className={styles.imageError}>
          <span>Failed to load image</span>
        </div>
      )}
    </div>
  );
};
