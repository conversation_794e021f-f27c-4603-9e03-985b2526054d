import { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@shared/store/store';
import { setPageImagesMap } from '@shared/store/documentSlice';
import apiClient from '../helpers/apiClient';

export interface ImageLoadState {
  isLoading: boolean;
  isTransitioning: boolean;
  hasError: boolean;
  loadProgress: number;
}

export interface ImageData {
  imageUrl?: string;
  thumbUrl?: string;
  loadState: ImageLoadState;
  lastLoaded?: number;
}

export interface UseImageLoaderOptions {
  enablePreloading?: boolean;
  preloadRange?: number; // Number of pages to preload ahead/behind
  transitionDuration?: number; // Animation duration in ms
  enableThumbnailFallback?: boolean;
}

const DEFAULT_OPTIONS: UseImageLoaderOptions = {
  enablePreloading: true,
  preloadRange: 2,
  transitionDuration: 300,
  enableThumbnailFallback: true,
};

export const useImageLoader = (options: UseImageLoaderOptions = {}) => {
  const dispatch = useDispatch();
  const opts = { ...DEFAULT_OPTIONS, ...options };

  // State management
  const [loadingStates, setLoadingStates] = useState<Record<string, ImageLoadState>>({});
  const [animationStates, setAnimationStates] = useState<Record<string, boolean>>({});

  // Refs for cleanup
  const abortControllersRef = useRef<Map<string, AbortController>>(new Map());
  const timeoutsRef = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // Redux selectors
  const pageImagesMap = useSelector((state: RootState) => state.document.pageImagesMap);
  const activeDocument = useSelector((state: RootState) => state.document.activeDocument);
  const activePageNo = useSelector((state: RootState) => state.document.activePageNo);

  // Cleanup function
  const cleanup = useCallback(() => {
    abortControllersRef.current.forEach((controller) => controller.abort());
    abortControllersRef.current.clear();

    timeoutsRef.current.forEach((timeout) => clearTimeout(timeout));
    timeoutsRef.current.clear();
  }, []);

  // Generate cache key for image
  const getCacheKey = useCallback((docId: string, pageNo: number, type: 'thumb' | 'full') => {
    return `${docId}-${pageNo}-${type}`;
  }, []);

  // Update loading state
  const updateLoadingState = useCallback((key: string, updates: Partial<ImageLoadState>) => {
    setLoadingStates((prev) => ({
      ...prev,
      [key]: { ...prev[key], ...updates },
    }));
  }, []);

  // Generate image URL
  const getImageUrl = useCallback(
    (inboxId: string, docId: string, pageNo: number, type: 'thumb' | 'full') => {
      const baseUrl = import.meta.env.VITE_PAPERBOX_CDN_URL;
      if (type === 'thumb') {
        return `${baseUrl}/inboxes/${inboxId}/documents/${docId}/pages/${pageNo}?w=490&q=30`;
      }
      return `${baseUrl}/inboxes/${inboxId}/documents/${docId}/pages/${pageNo}?w=2000`;
    },
    [],
  );

  // Load single image
  const loadImage = useCallback(
    async (
      inboxId: string,
      docId: string,
      pageNo: number,
      type: 'thumb' | 'full',
      priority: 'high' | 'normal' | 'low' = 'normal',
    ): Promise<string | null> => {
      const cacheKey = getCacheKey(docId, pageNo, type);

      // Check if already loaded
      const existingImage = pageImagesMap?.[docId]?.[pageNo];
      if (type === 'thumb' && existingImage?.thumbUrl) return existingImage.thumbUrl;
      if (type === 'full' && existingImage?.imageUrl) return existingImage.imageUrl;

      // Cancel any existing request for this image
      const existingController = abortControllersRef.current.get(cacheKey);
      if (existingController) {
        existingController.abort();
      }

      // Create new abort controller
      const controller = new AbortController();
      abortControllersRef.current.set(cacheKey, controller);

      // Initialize loading state
      updateLoadingState(cacheKey, {
        isLoading: true,
        hasError: false,
        loadProgress: 0,
        isTransitioning: false,
      });

      try {
        const url = getImageUrl(inboxId, docId, pageNo, type);

        const response = await apiClient.get(url, {
          signal: controller.signal,
          responseType: 'blob',
          headers: { accept: 'image/*' },
          onDownloadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const progress = (progressEvent.loaded / progressEvent.total) * 100;
              updateLoadingState(cacheKey, { loadProgress: progress });
            }
          },
        });

        // Convert blob to base64
        const base64String = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onloadend = () => resolve(reader.result as string);
          reader.onerror = reject;
          reader.readAsDataURL(response.data);
        });

        // Update Redux store
        dispatch(
          setPageImagesMap({
            docId,
            pageNo,
            [type === 'thumb' ? 'thumbUrl' : 'imageUrl']: base64String,
          }),
        );

        // Update loading state
        updateLoadingState(cacheKey, {
          isLoading: false,
          hasError: false,
          loadProgress: 100,
        });

        // Clean up controller
        abortControllersRef.current.delete(cacheKey);

        return base64String;
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error(`Error loading ${type} image for page ${pageNo}:`, error);
          updateLoadingState(cacheKey, {
            isLoading: false,
            hasError: true,
            loadProgress: 0,
          });
        }

        abortControllersRef.current.delete(cacheKey);
        return null;
      }
    },
    [getCacheKey, pageImagesMap, updateLoadingState, getImageUrl, dispatch],
  );

  // Load thumbnail first, then full image
  const loadImageWithThumbnail = useCallback(
    async (
      inboxId: string,
      docId: string,
      pageNo: number,
    ): Promise<{ thumbUrl?: string; imageUrl?: string }> => {
      const results: { thumbUrl?: string; imageUrl?: string } = {};

      // Load thumbnail first (high priority)
      if (opts.enableThumbnailFallback) {
        results.thumbUrl = await loadImage(inboxId, docId, pageNo, 'thumb', 'high');
      }

      // Load full image (normal priority)
      results.imageUrl = await loadImage(inboxId, docId, pageNo, 'full', 'normal');

      return results;
    },
    [loadImage, opts.enableThumbnailFallback],
  );

  // Preload adjacent pages
  const preloadAdjacentPages = useCallback(
    async (inboxId: string, docId: string, currentPageNo: number, totalPages: number) => {
      if (!opts.enablePreloading || !opts.preloadRange) return;

      const pagesToPreload: number[] = [];

      // Add pages before current
      for (let i = 1; i <= opts.preloadRange; i++) {
        const pageNo = currentPageNo - i;
        if (pageNo >= 1) pagesToPreload.push(pageNo);
      }

      // Add pages after current
      for (let i = 1; i <= opts.preloadRange; i++) {
        const pageNo = currentPageNo + i;
        if (pageNo <= totalPages) pagesToPreload.push(pageNo);
      }

      // Load thumbnails for all pages (low priority)
      const thumbnailPromises = pagesToPreload.map((pageNo) =>
        loadImage(inboxId, docId, pageNo, 'thumb', 'low'),
      );

      // Load full images for immediate neighbors (normal priority)
      const immediateNeighbors = [currentPageNo - 1, currentPageNo + 1].filter(
        (pageNo) => pageNo >= 1 && pageNo <= totalPages,
      );

      const fullImagePromises = immediateNeighbors.map((pageNo) =>
        loadImage(inboxId, docId, pageNo, 'full', 'normal'),
      );

      // Execute preloading
      await Promise.allSettled([...thumbnailPromises, ...fullImagePromises]);
    },
    [loadImage, opts.enablePreloading, opts.preloadRange],
  );

  // Trigger navigation animation
  const triggerNavigationAnimation = useCallback(
    (docId: string, pageNo: number, type: 'page' | 'document' = 'page') => {
      const animationKey = `${docId}-${pageNo}-${type}`;

      // Set animation state
      setAnimationStates((prev) => ({ ...prev, [animationKey]: true }));

      // Clear animation after duration
      const timeout = setTimeout(
        () => {
          setAnimationStates((prev) => ({ ...prev, [animationKey]: false }));
        },
        type === 'document' ? 400 : opts.transitionDuration,
      );

      timeoutsRef.current.set(animationKey, timeout);
    },
    [opts.transitionDuration],
  );

  // Get current image with fallback logic
  const getCurrentImage = useCallback(
    (docId: string, pageNo: number) => {
      const imageData = pageImagesMap?.[docId]?.[pageNo];
      const cacheKey = getCacheKey(docId, pageNo, 'full');
      const thumbCacheKey = getCacheKey(docId, pageNo, 'thumb');

      const loadState = loadingStates[cacheKey] || {
        isLoading: false,
        isTransitioning: false,
        hasError: false,
        loadProgress: 0,
      };

      const thumbLoadState = loadingStates[thumbCacheKey] || {
        isLoading: false,
        isTransitioning: false,
        hasError: false,
        loadProgress: 0,
      };

      // Determine which image to show
      let currentImageUrl = imageData?.imageUrl;
      let isShowingThumbnail = false;

      if (!currentImageUrl && opts.enableThumbnailFallback && imageData?.thumbUrl) {
        currentImageUrl = imageData.thumbUrl;
        isShowingThumbnail = true;
      }

      return {
        imageUrl: currentImageUrl,
        thumbUrl: imageData?.thumbUrl,
        isShowingThumbnail,
        loadState,
        thumbLoadState,
        isLoading: loadState.isLoading || (!currentImageUrl && thumbLoadState.isLoading),
        hasError: loadState.hasError && (!opts.enableThumbnailFallback || thumbLoadState.hasError),
      };
    },
    [pageImagesMap, loadingStates, getCacheKey, opts.enableThumbnailFallback],
  );

  // Get animation state
  const getAnimationState = useCallback(
    (docId: string, pageNo: number, type: 'page' | 'document' = 'page') => {
      const animationKey = `${docId}-${pageNo}-${type}`;
      return animationStates[animationKey] || false;
    },
    [animationStates],
  );

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    // Core loading functions
    loadImage,
    loadImageWithThumbnail,
    preloadAdjacentPages,

    // Animation functions
    triggerNavigationAnimation,
    getAnimationState,

    // State getters
    getCurrentImage,
    loadingStates,

    // Utilities
    cleanup,
    getCacheKey,
  };
};
