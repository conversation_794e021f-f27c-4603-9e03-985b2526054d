import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import {
  IDocumentEnriched,
  IDocumentEntity,
  IDocumentEntityComplexValue,
  IDocumentTopologyPartEnriched,
} from '@shared/helpers/converters/document';
import { PageTokens } from '@shared/models/document';
import { cloneDeep } from 'lodash';

// Define the state structure
interface DocumentState {
  // Document data
  mainDocument: IDocumentEnriched | null;
  activeDocument: IDocumentEnriched | null;
  mutations: IDocumentEnriched[];
  documentJSON: PageTokens[] | null;
  failedCheckTargets: Record<string, boolean> | null;

  // UI state
  activePageNo: number;
  selectedMutationId: string;
  isViewerLoaded: boolean;
  pageImagesMap: Record<
    string,
    Record<
      string,
      {
        imageUrl?: string;
        thumbUrl?: string;
        loadingState?: {
          isLoading: boolean;
          isTransitioning: boolean;
          hasError: boolean;
          loadProgress: number;
          lastLoaded?: number;
        };
      }
    >
  > | null;
  pageIndexMap: number[] | null;

  // Image loading and animation states
  imageLoadingStates: Record<
    string,
    {
      isLoading: boolean;
      isTransitioning: boolean;
      hasError: boolean;
      loadProgress: number;
      type: 'thumb' | 'full';
    }
  >;
  navigationAnimationState: {
    isAnimating: boolean;
    animationType: 'page' | 'document' | 'none';
    direction: 'forward' | 'backward' | 'none';
    progress: number;
  };

  // Operation state
  lockedDocId: string | null;
  isDeletingMutation: boolean;
  deletingMutationId: string | null;
  importMasterdataResultStatus: 'idle' | 'importing' | 'error';

  // Loading states
  isLoading: boolean;
  isJSONLoading: boolean;
  isMainDocLoading: boolean;
  mutationsLoading: boolean;
  allLoading: boolean;

  // Navigation
  nextSiblingId: string | null;
  prevSiblingId: string | null;
  historicalSiblings: { next: string | null; previous: string | null } | null;

  // Metadata
  inboxId: string | null;
  mainDocId: string | null;
  prevDocId: string | null;
  historical: boolean;

  // Error state
  error: any;
}

// Initial state
const initialState: DocumentState = {
  // Document data
  mainDocument: null,
  activeDocument: null,
  mutations: [],
  documentJSON: null,
  failedCheckTargets: null,

  // UI state
  activePageNo: 1,
  selectedMutationId: 'original',
  isViewerLoaded: false,
  pageImagesMap: null,
  pageIndexMap: null,

  // Image loading and animation states
  imageLoadingStates: {},
  navigationAnimationState: {
    isAnimating: false,
    animationType: 'none',
    direction: 'none',
    progress: 0,
  },

  // Operation state
  lockedDocId: null,
  isDeletingMutation: false,
  deletingMutationId: null,
  importMasterdataResultStatus: 'idle',

  // Loading states
  isLoading: false,
  isJSONLoading: false,
  isMainDocLoading: false,
  mutationsLoading: false,
  allLoading: false,

  // Navigation
  nextSiblingId: null,
  prevSiblingId: null,
  historicalSiblings: null,

  // Metadata
  inboxId: null,
  mainDocId: null,
  prevDocId: null,
  historical: false,

  // Error state
  error: null,
};

// Create the slice
export const documentSlice = createSlice({
  name: 'document',
  initialState,
  reducers: {
    // Document initialization
    initializeDocument: (
      state,
      action: PayloadAction<{
        mainDocId: string;
        inboxId: string;
        historical: boolean;
      }>,
    ) => {
      const { mainDocId, inboxId, historical } = action.payload;
      state.prevDocId = state.mainDocId;
      state.mainDocId = mainDocId;
      state.inboxId = inboxId;
      state.historical = historical;

      // Reset UI state when document changes
      if (state.prevDocId !== mainDocId) {
        state.activePageNo = 1;
        state.selectedMutationId = 'original';
        state.isViewerLoaded = false;
      }
    },

    // Document data setters
    setMainDocument: (state, action: PayloadAction<IDocumentEnriched | null>) => {
      state.mainDocument = action.payload;

      // Update active document if we're viewing the original
      if (state.selectedMutationId === 'original') {
        state.activeDocument = action.payload;
      }
    },

    setMutations: (state, action: PayloadAction<IDocumentEnriched[]>) => {
      state.mutations = action.payload;

      // Update active document if we're viewing a mutation
      if (state.selectedMutationId !== 'original') {
        const selectedMutation = action.payload.find((m) => m.id === state.selectedMutationId);
        if (selectedMutation) {
          state.activeDocument = selectedMutation;
        }
      }
    },

    setDocumentJSON: (state, action: PayloadAction<PageTokens[] | null>) => {
      state.documentJSON = action.payload;
    },

    // UI state setters
    setActivePage: (state, action: PayloadAction<number>) => {
      if (!state.pageIndexMap || state.pageIndexMap.length === 0) return;

      const boundary = state.pageIndexMap[state.pageIndexMap.length - 1];
      if (!boundary) return;

      const boundPageNo = Math.max(0, Math.min(boundary, action.payload));
      if (boundPageNo !== state.activePageNo) {
        state.isViewerLoaded = false;
        state.activePageNo = boundPageNo;
      }
    },

    setSelectedMutationId: (state, action: PayloadAction<string>) => {
      state.selectedMutationId = action.payload;

      // Update active document based on selection
      if (action.payload === 'original') {
        state.activeDocument = state.mainDocument;
      } else {
        const selectedMutation = state.mutations.find((m) => m.id === action.payload);
        if (selectedMutation) {
          state.activeDocument = selectedMutation;
        }
      }

      // Update page index map based on the new active document
      if (state.activeDocument?.topology) {
        const map: number[] = [];
        state.activeDocument.topology.parts.forEach((part) => {
          if (!part.archived) {
            map.push(...part.pages.filter((p) => !p.archived).map((e) => e.bundlePageNo));
          }
        });
        state.pageIndexMap = map;

        // Ensure active page is within bounds
        if (map.length > 0) {
          const maxPage = Math.max(...map);
          if (state.activePageNo > maxPage) {
            state.activePageNo = map.length > 0 ? map[0] : 1;
            state.isViewerLoaded = false;
          }
        }
      }
    },

    setIsViewerLoaded: (state, action: PayloadAction<boolean>) => {
      state.isViewerLoaded = action.payload;
    },

    setPageImagesMap: (
      state,
      action: PayloadAction<{
        docId: string;
        pageNo: number;
        imageUrl?: string;
        thumbUrl?: string;
        loadingState?: {
          isLoading: boolean;
          isTransitioning: boolean;
          hasError: boolean;
          loadProgress: number;
          lastLoaded?: number;
        };
      }>,
    ) => {
      const { docId, pageNo, imageUrl, thumbUrl, loadingState } = action.payload;

      if (!state.pageImagesMap) {
        state.pageImagesMap = {};
      }

      if (!state.pageImagesMap[docId]) {
        state.pageImagesMap[docId] = {};
      }

      if (!state.pageImagesMap[docId][pageNo]) {
        state.pageImagesMap[docId][pageNo] = {};
      }

      // Update image URLs
      if (imageUrl !== undefined) {
        state.pageImagesMap[docId][pageNo].imageUrl = imageUrl;
      }

      if (thumbUrl !== undefined) {
        state.pageImagesMap[docId][pageNo].thumbUrl = thumbUrl;
      }

      // Update loading state
      if (loadingState !== undefined) {
        state.pageImagesMap[docId][pageNo].loadingState = loadingState;
      }
    },

    // Enhanced image loading state management
    setImageLoadingState: (
      state,
      action: PayloadAction<{
        key: string;
        isLoading: boolean;
        isTransitioning?: boolean;
        hasError?: boolean;
        loadProgress?: number;
        type: 'thumb' | 'full';
      }>,
    ) => {
      const {
        key,
        isLoading,
        isTransitioning = false,
        hasError = false,
        loadProgress = 0,
        type,
      } = action.payload;

      state.imageLoadingStates[key] = {
        isLoading,
        isTransitioning,
        hasError,
        loadProgress,
        type,
      };
    },

    clearImageLoadingState: (state, action: PayloadAction<string>) => {
      delete state.imageLoadingStates[action.payload];
    },

    // Navigation animation state management
    setNavigationAnimationState: (
      state,
      action: PayloadAction<{
        isAnimating: boolean;
        animationType?: 'page' | 'document' | 'none';
        direction?: 'forward' | 'backward' | 'none';
        progress?: number;
      }>,
    ) => {
      const { isAnimating, animationType = 'none', direction = 'none', progress = 0 } = action.payload;

      state.navigationAnimationState = {
        isAnimating,
        animationType,
        direction,
        progress,
      };
    },

    // Clear images for a specific document (cleanup)
    clearDocumentImages: (state, action: PayloadAction<string>) => {
      const docId = action.payload;
      if (state.pageImagesMap?.[docId]) {
        delete state.pageImagesMap[docId];
      }

      // Clear related loading states
      Object.keys(state.imageLoadingStates).forEach((key) => {
        if (key.startsWith(`${docId}-`)) {
          delete state.imageLoadingStates[key];
        }
      });
    },

    updatePageIndexMap: (state) => {
      if (!state.activeDocument?.topology) {
        state.pageIndexMap = [];
        return;
      }

      const map: number[] = [];
      state.activeDocument.topology.parts.forEach((part) => {
        if (!part.archived) {
          map.push(...part.pages.filter((p) => !p.archived).map((e) => e.bundlePageNo));
        }
      });

      state.pageIndexMap = map;
    },

    // Operation state setters
    setLockedDocId: (state, action: PayloadAction<string | null>) => {
      state.lockedDocId = action.payload;
    },

    setIsDeletingMutation: (state, action: PayloadAction<boolean>) => {
      state.isDeletingMutation = action.payload;
    },

    setDeletingMutationId: (state, action: PayloadAction<string | null>) => {
      state.deletingMutationId = action.payload;
    },

    setImportMasterdataResultStatus: (state, action: PayloadAction<'idle' | 'importing' | 'error'>) => {
      state.importMasterdataResultStatus = action.payload;
    },

    // Loading state setters
    setIsLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    setIsJSONLoading: (state, action: PayloadAction<boolean>) => {
      state.isJSONLoading = action.payload;
    },

    setIsMainDocLoading: (state, action: PayloadAction<boolean>) => {
      state.isMainDocLoading = action.payload;
    },

    setMutationsLoading: (state, action: PayloadAction<boolean>) => {
      state.mutationsLoading = action.payload;
    },

    updateAllLoading: (state) => {
      state.allLoading = state.isJSONLoading || state.mutationsLoading || state.isMainDocLoading;
    },

    // Navigation setters
    setNextSiblingId: (state, action: PayloadAction<string | null>) => {
      state.nextSiblingId = action.payload;
    },

    setPrevSiblingId: (state, action: PayloadAction<string | null>) => {
      state.prevSiblingId = action.payload;
    },

    setHistoricalSiblings: (
      state,
      action: PayloadAction<{ next: string | null; previous: string | null } | null>,
    ) => {
      state.historicalSiblings = action.payload;
    },

    // Error state
    setError: (state, action: PayloadAction<any>) => {
      state.error = action.payload;
    },

    // Entity operations - optimistic updates
    addEntityOptimistic: (
      state,
      action: PayloadAction<{
        entity: IDocumentEntity;
      }>,
    ) => {
      const { entity } = action.payload;

      if (state.selectedMutationId !== 'original' && state.activeDocument) {
        if (!state.activeDocument.entities) state.activeDocument.entities = [];
        state.activeDocument.entities.push(entity);
      } else if (state.mainDocument) {
        if (!state.mainDocument.entities) state.mainDocument.entities = [];
        state.mainDocument.entities.push(entity);

        // Also update activeDocument if it's the same as mainDocument
        if (state.activeDocument === state.mainDocument) {
          state.activeDocument = state.mainDocument;
        }
      }
    },

    editEntityOptimistic: (
      state,
      action: PayloadAction<{
        entityId: string;
        childId?: string;
        updatedEntity: IDocumentEntity;
      }>,
    ) => {
      const { entityId, childId, updatedEntity } = action.payload;

      if (!state.activeDocument?.entities) return;

      const entityIndex = state.activeDocument.entities.findIndex((e) => e.id === entityId);
      if (entityIndex === -1) return;

      if (childId) {
        // Handle complex field update
        const complexValue = cloneDeep(
          state.activeDocument.entities[entityIndex].value,
        ) as IDocumentEntityComplexValue;
        if (complexValue?.complex?.[childId]) {
          complexValue.complex[childId] = { ...complexValue.complex[childId], ...updatedEntity };
          state.activeDocument.entities[entityIndex].value = complexValue;
        }
      } else {
        // Handle regular entity update
        state.activeDocument.entities[entityIndex] = updatedEntity;
      }

      // If we're editing the main document, update it as well
      if (state.selectedMutationId === 'original' && state.mainDocument) {
        state.mainDocument = state.activeDocument;
      } else if (state.selectedMutationId !== 'original') {
        // Update the mutation in the mutations array
        const mutationIndex = state.mutations.findIndex((m) => m.id === state.selectedMutationId);
        if (mutationIndex !== -1) {
          state.mutations[mutationIndex] = state.activeDocument;
        }
      }
    },

    deleteEntityOptimistic: (
      state,
      action: PayloadAction<{
        entityId: string;
        childId?: string;
      }>,
    ) => {
      const { entityId, childId } = action.payload;

      if (!state.activeDocument?.entities) return;

      if (childId) {
        // Handle complex field deletion
        const entityIndex = state.activeDocument.entities.findIndex((e) => e.id === entityId);
        if (entityIndex === -1) return;

        const complexValue = cloneDeep(
          state.activeDocument.entities[entityIndex].value,
        ) as IDocumentEntityComplexValue;
        if (complexValue?.complex?.[childId]) {
          // Clear the child value but keep the field
          complexValue.complex[childId] = {
            id: childId,
            pageNo: null,
            rawValue: null,
            value: null,
            confidence: 0,
            type: complexValue.complex[childId].type,
            valueLocations: [],
            source: 'user',
          };
          state.activeDocument.entities[entityIndex].value = complexValue;
        }
      } else {
        // Handle regular entity deletion
        state.activeDocument.entities = state.activeDocument.entities.filter((e) => e.id !== entityId);
      }

      // If we're editing the main document, update it as well
      if (state.selectedMutationId === 'original' && state.mainDocument) {
        state.mainDocument = state.activeDocument;
      } else if (state.selectedMutationId !== 'original') {
        // Update the mutation in the mutations array
        const mutationIndex = state.mutations.findIndex((m) => m.id === state.selectedMutationId);
        if (mutationIndex !== -1) {
          state.mutations[mutationIndex] = state.activeDocument;
        }
      }
    },

    // Topology operations
    updateTopologyPartOptimistic: (
      state,
      action: PayloadAction<{
        partId: string;
        updates: Partial<IDocumentTopologyPartEnriched>;
      }>,
    ) => {
      const { partId, updates } = action.payload;

      if (!state.activeDocument?.topology?.parts) return;

      const partIndex = state.activeDocument.topology.parts.findIndex((p) => p.id === partId);
      if (partIndex === -1) return;

      state.activeDocument.topology.parts[partIndex] = {
        ...state.activeDocument.topology.parts[partIndex],
        ...updates,
      };

      // Update page index map after topology changes
      documentSlice.caseReducers.updatePageIndexMap(state);
    },

    // Set failed check targets
    setFailedCheckTargets: (state, action: PayloadAction<Record<string, boolean> | null>) => {
      state.failedCheckTargets = action.payload;
    },

    // Reset state
    resetDocumentState: () => {
      return initialState;
    },
  },
});

// Export actions
export const {
  initializeDocument,
  setMainDocument,
  setMutations,
  setDocumentJSON,
  setActivePage,
  setSelectedMutationId,
  setIsViewerLoaded,
  setPageImagesMap,
  setImageLoadingState,
  clearImageLoadingState,
  setNavigationAnimationState,
  clearDocumentImages,
  updatePageIndexMap,
  setLockedDocId,
  setIsDeletingMutation,
  setDeletingMutationId,
  setImportMasterdataResultStatus,
  setIsLoading,
  setIsJSONLoading,
  setIsMainDocLoading,
  setMutationsLoading,
  updateAllLoading,
  setNextSiblingId,
  setPrevSiblingId,
  setHistoricalSiblings,
  setError,
  addEntityOptimistic,
  editEntityOptimistic,
  deleteEntityOptimistic,
  updateTopologyPartOptimistic,
  setFailedCheckTargets,
  resetDocumentState,
} = documentSlice.actions;

// Selectors
export const selectMainDocument = (state: { document: DocumentState }) => state.document.mainDocument;
export const selectActiveDocument = (state: { document: DocumentState }) => state.document.activeDocument;
export const selectMutations = (state: { document: DocumentState }) => state.document.mutations;
export const selectDocumentJSON = (state: { document: DocumentState }) => state.document.documentJSON;
export const selectActivePageNo = (state: { document: DocumentState }) => state.document.activePageNo;
export const selectSelectedMutationId = (state: { document: DocumentState }) =>
  state.document.selectedMutationId;
export const selectIsViewerLoaded = (state: { document: DocumentState }) => state.document.isViewerLoaded;
export const selectPageImagesMap = (state: { document: DocumentState }) => state.document.pageImagesMap;
export const selectPageIndexMap = (state: { document: DocumentState }) => state.document.pageIndexMap;
export const selectLockedDocId = (state: { document: DocumentState }) => state.document.lockedDocId;
export const selectIsDeletingMutation = (state: { document: DocumentState }) =>
  state.document.isDeletingMutation;
export const selectDeletingMutationId = (state: { document: DocumentState }) =>
  state.document.deletingMutationId;
export const selectImportMasterdataResultStatus = (state: { document: DocumentState }) =>
  state.document.importMasterdataResultStatus;
export const selectIsLoading = (state: { document: DocumentState }) => state.document.isLoading;
export const selectIsJSONLoading = (state: { document: DocumentState }) => state.document.isJSONLoading;
export const selectIsMainDocLoading = (state: { document: DocumentState }) => state.document.isMainDocLoading;
export const selectMutationsLoading = (state: { document: DocumentState }) => state.document.mutationsLoading;
export const selectAllLoading = (state: { document: DocumentState }) => state.document.allLoading;
export const selectNextSiblingId = (state: { document: DocumentState }) => state.document.nextSiblingId;
export const selectPrevSiblingId = (state: { document: DocumentState }) => state.document.prevSiblingId;
export const selectHistoricalSiblings = (state: { document: DocumentState }) =>
  state.document.historicalSiblings;
export const selectInboxId = (state: { document: DocumentState }) => state.document.inboxId;
export const selectMainDocId = (state: { document: DocumentState }) => state.document.mainDocId;
export const selectHistorical = (state: { document: DocumentState }) => state.document.historical;
export const selectError = (state: { document: DocumentState }) => state.document.error;
export const selectFailedCheckTargets = (state: { document: DocumentState }) =>
  state.document.failedCheckTargets;

// Derived selectors
export const selectIsMutation = (state: { document: DocumentState }) =>
  state.document.selectedMutationId !== 'original';

export const selectIsInteractive = (state: { document: DocumentState }) => {
  const { activeDocument, lockedDocId, mainDocId } = state.document;

  if (activeDocument?.action) return false;
  if (lockedDocId !== mainDocId) return false;

  return true;
};

export const selectEffectiveNextSiblingId = (state: { document: DocumentState }) => {
  const { historical, historicalSiblings, nextSiblingId } = state.document;
  return historical ? historicalSiblings?.next || null : nextSiblingId;
};

export const selectEffectivePrevSiblingId = (state: { document: DocumentState }) => {
  const { historical, historicalSiblings, prevSiblingId } = state.document;
  return historical ? historicalSiblings?.previous || null : prevSiblingId;
};

export default documentSlice.reducer;
